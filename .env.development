ENV = 'development'
VITE_CLI_PORT = 8920
# VITE_SERVER_PORT = 8910
VITE_SERVER_PORT = 8910
VITE_BASE_API = /api
VITE_FILE_API = /api
# VITE_BASE_PATH = https://zcadmin.costnovel.com/api
# VITE_BASE_PATH = https://zc-wechat.costnovel.com/api
# VITE_BASE_PATH = http://*************
# 测试环境地址
# VITE_BASE_PATH = http://**************
VITE_BASE_PATH = https://retail-admin.costnovel.com
VITE_EDITOR = vscode
// VITE_EDITOR = webstorm 如果使用webstorm开发且要使用dom定位到代码行功能 请先自定添加 webstorm到环境变量 再将VITE_EDITOR值修改为webstorm
// 如果使用docker-compose开发模式，设置为下面的地址或本机主机IP
//VITE_BASE_PATH = http://**********

VITE_OSS_PREFIX = http://zcstatic.costnovel.com/