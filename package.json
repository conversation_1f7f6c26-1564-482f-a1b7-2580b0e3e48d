{"name": "gin-vue-admin", "version": "2.5.7", "private": true, "scripts": {"serve": "vite --host --mode development", "test": "cross-env NODE_ENV=test vite build --mode test", "build": "vite build --mode production", "limit-build": "npm install increase-memory-limit-fixbug cross-env -g && npm run fix-memory-limit && node ./limit && npm run build", "preview": "vite preview", "fix-memory-limit": "cross-env LIMIT=4096 increase-memory-limit"}, "dependencies": {"@element-plus/icons-vue": "^2.1.0", "@vue-office/docx": "^1.3.0", "@vue-office/excel": "^1.4.5", "@vue-office/pdf": "^1.5.3", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "axios": "^1.4.0", "core-js": "^3.31.1", "echarts": "5.4.3", "element-plus": "^2.3.8", "exceljs": "^4.4.0", "file-saver": "^2.0.5", "highlight.js": "^11.8.0", "marked": "4.3.0", "mitt": "^3.0.1", "nprogress": "^0.2.0", "path": "^0.12.7", "pinia": "^2.1.4", "qs": "^6.11.2", "screenfull": "^6.0.2", "sortablejs": "^1.15.6", "spark-md5": "^3.0.2", "tailwindcss": "^3.3.3", "vue": "^3.3.4", "vue-router": "^4.2.4", "vuedraggable": "^4.1.0", "xlsx": "^0.18.5", "vue-clipboard3": "^2.0.0"}, "devDependencies": {"@babel/eslint-parser": "^7.22.9", "@vitejs/plugin-legacy": "^4.1.0", "@vitejs/plugin-vue": "^4.2.3", "@vue/cli-plugin-babel": "~5.0.8", "@vue/cli-plugin-eslint": "~5.0.8", "@vue/cli-plugin-router": "~5.0.8", "@vue/cli-plugin-vuex": "~5.0.8", "@vue/cli-service": "~5.0.8", "@vue/compiler-sfc": "^3.3.4", "babel-plugin-import": "^1.13.6", "chalk": "^4.1.2", "dotenv": "^16.3.1", "eslint": "^8.49.0", "eslint-plugin-vue": "^9.15.1", "sass": "^1.54.0", "terser": "^5.19.1", "unplugin-auto-import": "^0.16.6", "unplugin-vue-components": "^0.25.1", "vite": "^4.4.6", "vite-plugin-banner": "^0.7.0", "vite-plugin-importer": "^0.2.5"}}