<template>
  <div id="app">
    <el-config-provider :locale="zhCn">
      <router-view />
    </el-config-provider>
  </div>
</template>

<script setup>
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
// element 2.3.8之前使用下面的语句
// import zhCn from 'element-plus/lib/locale/lang/zh-cn'

defineOptions({
  name: 'App'
})

</script>
<style lang="scss">
@tailwind base;
@tailwind components;
@tailwind utilities;
// 引入初始化样式
#app {
  background: #eee;
  height: 100vh;
  overflow: hidden;
  font-weight: 400 !important;
}
.el-button{
  font-weight: 400 !important;
}

// 单元格样式
.el-table__cell {
  position: static !important; // 解决el-image 和 el-table冲突层级冲突问题
}

.img-col {
  .cell {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
.el-form--inline .el-form-item .el-select{
  width: 200px !important;
}

</style>
