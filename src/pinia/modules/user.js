import {getUserInfo, login, setSelfInfo} from '@/api/user'
import {jsonInBlacklist} from '@/api/jwt'
import router from '@/router/index'
import {ElLoading, ElMessage} from 'element-plus'
import {defineStore} from 'pinia'
import {computed, ref, watch} from 'vue'
import {useRouterStore} from './router'
import {getRetailAreasAll} from "@/api/retail/retailAreas";
import {getOrderStateMap} from "@/api/retail/orders";
import {getBusinessTypeMapInfo} from "@/api/retail/userFeedback";

export const useUserStore = defineStore('user', () => {
  const loadingInstance = ref(null)
  const areasList = ref([])
  const businessTypeList = ref([])
  const businessTypeMap = ref({})
  const catTypeList = ref([
    {label: '外卖', value: 1},
    {label: '团购', value: 2},
    {label: '预订', value: 3},
  ])
  const weekDayList = ref([
    {label: '一', value: 1},
    {label: '二', value: 2},
    {label: '三', value: 3},
    {label: '四', value: 4},
    {label: '五', value: 5},
    // {label: '六', value: 6},
    // {label: '日', value: 7},
  ])
  const catTypeMap = ref({
    1: '外卖',
    2: '团购',
    3: '预订',
  })
  const areasMap = ref({})
  const orderStateMap = ref({})
  const orderStateOptions = ref([])

  const userInfo = ref({
    uuid: '',
    nickName: '',
    headerImg: '',
    authority: {},
    sideMode: 'dark',
    activeColor: 'var(--el-color-primary)',
    baseColor: '#fff'
  })
  const token = ref(window.localStorage.getItem('token') || '')
  const setUserInfo = (val) => {
    userInfo.value = val
  }
  const setAreasList = (val) => {
    areasList.value = val
  }
  const setAreasMap = (val) => {
    areasMap.value = val
  }
  const setOrderStateMap = (val) => {
    orderStateMap.value = val
  }
  const setOrderStateOptions = (val) => {
    orderStateOptions.value = val
  }
  const setBusinessTypeList = (val) => {
    businessTypeList.value = val
  }
  const setBusinessTypeMap = (val) => {
    businessTypeMap.value = val
  }

  const setToken = (val) => {
    token.value = val
  }

  const NeedInit = () => {
    token.value = ''
    window.localStorage.removeItem('token')
    localStorage.clear()
    router.push({ name: 'Init', replace: true })
  }

  const ResetUserInfo = (value = {}) => {
    userInfo.value = {
      ...userInfo.value,
      ...value
    }
  }
  /* 获取用户信息*/
  const GetUserInfo = async() => {
    const res = await getUserInfo()
    if (res.code === 0) {
      setUserInfo(res.data.userInfo)
    }
    return res
  }

  const InitAllAreasInfo = async () => {
    const res = await getRetailAreasAll()
    if (res.code === 0) {
      let resList = res.data.list || []
      let newMap = {}
      resList.forEach(item => {
        newMap[item.id] = item
      })
      setAreasList(resList)
      setAreasMap(newMap)
    }
    return res
  }
  const InitBusinessTypeInfo = async () => {
    const res = await getBusinessTypeMapInfo()
    if (res.code === 0) {
      let resMap = res.data || {}
      let resMapNew = {}
      let resList = []
      Object.keys(resMap).forEach(key => {
        resMapNew[parseInt(key)] = resMap[key]
        resList.push({
          label: resMap[key],
          value: parseInt(key)
        })
      })
      setBusinessTypeList(resList)
      setBusinessTypeMap(resMapNew)
    }
    return res
  }

  const InitOrderStateMap = async () => {
    const res = await getOrderStateMap()
    if (res.code === 0) {
      let resMap = res.data || {}
      let optionsList = []
      // 循环map,生成optionsList
      for (let key in resMap) {
        optionsList.push({
          label: resMap[key],
          value: parseInt(key)
        })
      }
      setOrderStateMap(resMap)
      setOrderStateOptions(optionsList)

    }
    return res
  }

  const GetAreaItem = (areaId) => {
    return areasMap.value[areaId]
  }

  const GetOrderState = (s) => {
    return orderStateMap.value[s]
  }
  const GetAreaName = (areaId) => {
    let item = GetAreaItem(areaId)
    if (item) {
      return item.name
    } else {
      return `未知(${areaId})`
    }
  }
  const GetBusinessTypeName = (bType) => {
    let name = businessTypeMap.value[bType]
    if (name) {
      return name
    } else {
      return `未知(${bType})`
    }
  }
  const GetCatTypeName = (catType) => {
    let name = catTypeMap.value[catType]
    if (name) {
      return name
    } else {
      return `未知(${catType})`
    }
  }
  /* 登录*/
  const LoginIn = async(loginInfo) => {
    loadingInstance.value = ElLoading.service({
      fullscreen: true,
      text: '登录中，请稍候...',
    })
    try {
      const res = await login(loginInfo)
      if (res.code === 0) {
        setUserInfo(res.data.user)
        setToken(res.data.token)
        const routerStore = useRouterStore()
        await routerStore.SetAsyncRouter()
        const asyncRouters = routerStore.asyncRouters
        asyncRouters.forEach(asyncRouter => {
          router.addRoute(asyncRouter)
        })

        if (!router.hasRoute(userInfo.value.authority.defaultRouter)) {
          ElMessage.error('请联系管理员进行授权')
        } else {
          await router.replace({ name: userInfo.value.authority.defaultRouter })
        }

        loadingInstance.value.close()

        const isWin = ref(/windows/i.test(navigator.userAgent))
        if (isWin.value) {
          window.localStorage.setItem('osType', 'WIN')
        } else {
          window.localStorage.setItem('osType', 'MAC')
        }
        return true
      }
    } catch (e) {
      loadingInstance.value.close()
    }
    loadingInstance.value.close()
  }
  /* 登出*/
  const LoginOut = async() => {
    const res = await jsonInBlacklist()
    if (res.code === 0) {
      token.value = ''
      sessionStorage.clear()
      localStorage.clear()
      router.push({ name: 'Login', replace: true })
      window.location.reload()
    }
  }
  /* 清理数据 */
  const ClearStorage = async() => {
    token.value = ''
    sessionStorage.clear()
    localStorage.clear()
  }
  /* 设置侧边栏模式*/
  const changeSideMode = async(data) => {
    const res = await setSelfInfo({ sideMode: data })
    if (res.code === 0) {
      userInfo.value.sideMode = data
      ElMessage({
        type: 'success',
        message: '设置成功'
      })
    }
  }

  const mode = computed(() => userInfo.value.sideMode)
  const sideMode = computed(() => {
    if (userInfo.value.sideMode === 'dark') {
      return '#191a23'
    } else if (userInfo.value.sideMode === 'light') {
      return '#fff'
    } else {
      return userInfo.value.sideMode
    }
  })
  const baseColor = computed(() => {
    if (userInfo.value.sideMode === 'dark') {
      return '#fff'
    } else if (userInfo.value.sideMode === 'light') {
      return '#191a23'
    } else {
      return userInfo.value.baseColor
    }
  })
  const activeColor = computed(() => {
    return 'var(--el-color-primary)'
  })

  watch(() => token.value, () => {
    window.localStorage.setItem('token', token.value)
  })

  return {
    userInfo,
    token,
    NeedInit,
    ResetUserInfo,
    GetUserInfo,
    InitAllAreasInfo,
    InitBusinessTypeInfo,
    InitOrderStateMap,
    GetAreaItem,
    GetAreaName,
    GetOrderState,
    GetCatTypeName,
    GetBusinessTypeName,
    LoginIn,
    LoginOut,
    changeSideMode,
    mode,
    sideMode,
    orderStateMap,
    orderStateOptions,
    businessTypeList,
    businessTypeMap,
    areasList,
    catTypeList,
    weekDayList,
    areasMap,
    setToken,
    baseColor,
    activeColor,
    loadingInstance,
    ClearStorage
  }
})
