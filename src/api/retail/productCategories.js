import service from '@/utils/request'

// @Tags ProductCategories
// @Summary 创建productCategories表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.ProductCategories true "创建productCategories表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"创建成功"}"
// @Router /productCategories/createProductCategories [post]
export const createProductCategories = (data) => {
  return service({
    url: '/productCategories/createProductCategories',
    method: 'post',
    data
  })
}

// @Tags ProductCategories
// @Summary 删除productCategories表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.ProductCategories true "删除productCategories表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /productCategories/deleteProductCategories [delete]
export const deleteProductCategories = (data) => {
  return service({
    url: '/productCategories/deleteProductCategories',
    method: 'delete',
    data
  })
}

// @Tags ProductCategories
// @Summary 批量删除productCategories表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除productCategories表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /productCategories/deleteProductCategories [delete]
export const deleteProductCategoriesByIds = (data) => {
  return service({
    url: '/productCategories/deleteProductCategoriesByIds',
    method: 'delete',
    data
  })
}

// @Tags ProductCategories
// @Summary 更新productCategories表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.ProductCategories true "更新productCategories表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /productCategories/updateProductCategories [put]
export const updateProductCategories = (data) => {
  return service({
    url: '/productCategories/updateProductCategories',
    method: 'put',
    data
  })
}

// @Tags ProductCategories
// @Summary 用id查询productCategories表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query model.ProductCategories true "用id查询productCategories表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /productCategories/findProductCategories [get]
export const findProductCategories = (params) => {
  return service({
    url: '/productCategories/findProductCategories',
    method: 'get',
    params
  })
}

// @Tags ProductCategories
// @Summary 分页获取productCategories表列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取productCategories表列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /productCategories/getProductCategoriesList [get]
export const getProductCategoriesList = (params) => {
  return service({
    url: '/productCategories/getProductCategoriesList',
    method: 'get',
    params
  })
}

export const getProductCategoriesAll = (params) => {
  return service({
    url: '/productCategories/getProductCategoriesAll',
    method: 'get',
    params
  })
}

export const productCategoriesAll = (params) => {
  return service({
    url: '/productCategories/productCategoriesAll',
    method: 'get',
    params
  })
}
