import service from '@/utils/request'

export const createBusNews = (data) => {
  return service({
    url: '/busNews/createBusNews',
    method: 'post',
    data,
  })
}

export const updateBusNews = (data) => {
  return service({
    url: '/busNews/updateBusNews',
    method: 'put',
    data,
  })
}
export const deleteBusNews = (data) => {
  return service({
    url: '/busNews/deleteBusNews',
    method: 'delete',
    data,
  })
}
export const getBusNewsList = (params) => {
  return service({
    url: '/busNews/getBusNewsList',
    method: 'get',
    params,
  })
}
export const findBusNews = (params) => {
  return service({
    url: '/busNews/findBusNews',
    method: 'get',
    params,
  })
}
