import service from '@/utils/request'

// @Tags BusRouteSiteRelation
// @Summary 创建busRouteSiteRelation表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.BusRouteSiteRelation true "创建busRouteSiteRelation表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"创建成功"}"
// @Router /busRouteSiteRelation/createBusRouteSiteRelation [post]
export const createBusRouteSiteRelation = (data) => {
  return service({
    url: '/busRouteSiteRelation/createBusRouteSiteRelation',
    method: 'post',
    data
  })
}

// @Tags BusRouteSiteRelation
// @Summary 删除busRouteSiteRelation表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.BusRouteSiteRelation true "删除busRouteSiteRelation表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /busRouteSiteRelation/deleteBusRouteSiteRelation [delete]
export const deleteBusRouteSiteRelation = (data) => {
  return service({
    url: '/busRouteSiteRelation/deleteBusRouteSiteRelation',
    method: 'delete',
    data
  })
}

// @Tags BusRouteSiteRelation
// @Summary 批量删除busRouteSiteRelation表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除busRouteSiteRelation表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /busRouteSiteRelation/deleteBusRouteSiteRelation [delete]
export const deleteBusRouteSiteRelationByIds = (data) => {
  return service({
    url: '/busRouteSiteRelation/deleteBusRouteSiteRelationByIds',
    method: 'delete',
    data
  })
}

// @Tags BusRouteSiteRelation
// @Summary 更新busRouteSiteRelation表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.BusRouteSiteRelation true "更新busRouteSiteRelation表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /busRouteSiteRelation/updateBusRouteSiteRelation [put]
export const updateBusRouteSiteRelation = (data) => {
  return service({
    url: '/busRouteSiteRelation/updateBusRouteSiteRelation',
    method: 'put',
    data
  })
}

// @Tags BusRouteSiteRelation
// @Summary 用id查询busRouteSiteRelation表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query model.BusRouteSiteRelation true "用id查询busRouteSiteRelation表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /busRouteSiteRelation/findBusRouteSiteRelation [get]
export const findBusRouteSiteRelation = (params) => {
  return service({
    url: '/busRouteSiteRelation/findBusRouteSiteRelation',
    method: 'get',
    params
  })
}

// @Tags BusRouteSiteRelation
// @Summary 分页获取busRouteSiteRelation表列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取busRouteSiteRelation表列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /busRouteSiteRelation/getBusRouteSiteRelationList [get]
export const getBusRouteSiteRelationList = (params) => {
  return service({
    url: '/busRouteSiteRelation/getBusRouteSiteRelationList',
    method: 'get',
    params
  })
}
