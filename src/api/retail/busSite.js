import service from '@/utils/request'

// @Tags BusSite
// @Summary 创建busSite表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.BusSite true "创建busSite表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"创建成功"}"
// @Router /busSite/createBusSite [post]
export const createBusSite = (data) => {
  return service({
    url: '/busSite/createBusSite',
    method: 'post',
    data
  })
}

// @Tags BusSite
// @Summary 删除busSite表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.BusSite true "删除busSite表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /busSite/deleteBusSite [delete]
export const deleteBusSite = (data) => {
  return service({
    url: '/busSite/deleteBusSite',
    method: 'delete',
    data
  })
}

// @Tags BusSite
// @Summary 批量删除busSite表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除busSite表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /busSite/deleteBusSite [delete]
export const deleteBusSiteByIds = (data) => {
  return service({
    url: '/busSite/deleteBusSiteByIds',
    method: 'delete',
    data
  })
}

// @Tags BusSite
// @Summary 更新busSite表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.BusSite true "更新busSite表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /busSite/updateBusSite [put]
export const updateBusSite = (data) => {
  return service({
    url: '/busSite/updateBusSite',
    method: 'put',
    data
  })
}

// @Tags BusSite
// @Summary 用id查询busSite表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query model.BusSite true "用id查询busSite表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /busSite/findBusSite [get]
export const findBusSite = (params) => {
  return service({
    url: '/busSite/findBusSite',
    method: 'get',
    params
  })
}

// @Tags BusSite
// @Summary 分页获取busSite表列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取busSite表列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /busSite/getBusSiteList [get]
export const getBusSiteList = (params) => {
  return service({
    url: '/busSite/getBusSiteList',
    method: 'get',
    params
  })
}
