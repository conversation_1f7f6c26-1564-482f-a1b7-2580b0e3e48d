import service from '@/utils/request'

// @Tags RetailAreas
// @Summary 创建retailAreas表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.RetailAreas true "创建retailAreas表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"创建成功"}"
// @Router /retailAreas/createRetailAreas [post]
export const createRetailAreas = (data) => {
  return service({
    url: '/retailAreas/createRetailAreas',
    method: 'post',
    data
  })
}

// @Tags RetailAreas
// @Summary 删除retailAreas表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.RetailAreas true "删除retailAreas表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /retailAreas/deleteRetailAreas [delete]
export const deleteRetailAreas = (data) => {
  return service({
    url: '/retailAreas/deleteRetailAreas',
    method: 'delete',
    data
  })
}

// @Tags RetailAreas
// @Summary 批量删除retailAreas表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除retailAreas表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /retailAreas/deleteRetailAreas [delete]
export const deleteRetailAreasByIds = (data) => {
  return service({
    url: '/retailAreas/deleteRetailAreasByIds',
    method: 'delete',
    data
  })
}

// @Tags RetailAreas
// @Summary 更新retailAreas表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.RetailAreas true "更新retailAreas表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /retailAreas/updateRetailAreas [put]
export const updateRetailAreas = (data) => {
  return service({
    url: '/retailAreas/updateRetailAreas',
    method: 'put',
    data
  })
}

// @Tags RetailAreas
// @Summary 用id查询retailAreas表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query model.RetailAreas true "用id查询retailAreas表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /retailAreas/findRetailAreas [get]
export const findRetailAreas = (params) => {
  return service({
    url: '/retailAreas/findRetailAreas',
    method: 'get',
    params
  })
}

// @Tags RetailAreas
// @Summary 分页获取retailAreas表列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取retailAreas表列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /retailAreas/getRetailAreasList [get]
export const getRetailAreasList = (params) => {
  return service({
    url: '/retailAreas/getRetailAreasList',
    method: 'get',
    params
  })
}
export const getRetailAreasAll = (params) => {
  return service({
    url: '/retailAreas/getRetailAreasAll',
    method: 'get',
    params
  })
}
