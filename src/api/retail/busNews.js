import service from '@/utils/request'

// @Tags BusNews
// @Summary 创建busNews表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.BusNews true "创建busNews表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"创建成功"}"
// @Router /busNews/createBusNews [post]
export const createBusNews = (data) => {
  return service({
    url: '/busNews/createBusNews',
    method: 'post',
    data
  })
}

// @Tags BusNews
// @Summary 删除busNews表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.BusNews true "删除busNews表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /busNews/deleteBusNews [delete]
export const deleteBusNews = (data) => {
  return service({
    url: '/busNews/deleteBusNews',
    method: 'delete',
    data
  })
}

// @Tags BusNews
// @Summary 批量删除busNews表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除busNews表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /busNews/deleteBusNews [delete]
export const deleteBusNewsByIds = (data) => {
  return service({
    url: '/busNews/deleteBusNewsByIds',
    method: 'delete',
    data
  })
}

// @Tags BusNews
// @Summary 更新busNews表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.BusNews true "更新busNews表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /busNews/updateBusNews [put]
export const updateBusNews = (data) => {
  return service({
    url: '/busNews/updateBusNews',
    method: 'put',
    data
  })
}

// @Tags BusNews
// @Summary 用id查询busNews表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query model.BusNews true "用id查询busNews表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /busNews/findBusNews [get]
export const findBusNews = (params) => {
  return service({
    url: '/busNews/findBusNews',
    method: 'get',
    params
  })
}

// @Tags BusNews
// @Summary 分页获取busNews表列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取busNews表列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /busNews/getBusNewsList [get]
export const getBusNewsList = (params) => {
  return service({
    url: '/busNews/getBusNewsList',
    method: 'get',
    params
  })
}
