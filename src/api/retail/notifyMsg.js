import service from '@/utils/request'

// @Tags NotifyMsg
// @Summary 创建notifyMsg表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.NotifyMsg true "创建notifyMsg表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"创建成功"}"
// @Router /notifyMsg/createNotifyMsg [post]
export const createNotifyMsg = (data) => {
  return service({
    url: '/notifyMsg/createNotifyMsg',
    method: 'post',
    data
  })
}

// @Tags NotifyMsg
// @Summary 删除notifyMsg表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.NotifyMsg true "删除notifyMsg表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /notifyMsg/deleteNotifyMsg [delete]
export const deleteNotifyMsg = (data) => {
  return service({
    url: '/notifyMsg/deleteNotifyMsg',
    method: 'delete',
    data
  })
}

// @Tags NotifyMsg
// @Summary 批量删除notifyMsg表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除notifyMsg表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /notifyMsg/deleteNotifyMsg [delete]
export const deleteNotifyMsgByIds = (data) => {
  return service({
    url: '/notifyMsg/deleteNotifyMsgByIds',
    method: 'delete',
    data
  })
}

// @Tags NotifyMsg
// @Summary 更新notifyMsg表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.NotifyMsg true "更新notifyMsg表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /notifyMsg/updateNotifyMsg [put]
export const updateNotifyMsg = (data) => {
  return service({
    url: '/notifyMsg/updateNotifyMsg',
    method: 'put',
    data
  })
}

// @Tags NotifyMsg
// @Summary 用id查询notifyMsg表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query model.NotifyMsg true "用id查询notifyMsg表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /notifyMsg/findNotifyMsg [get]
export const findNotifyMsg = (params) => {
  return service({
    url: '/notifyMsg/findNotifyMsg',
    method: 'get',
    params
  })
}

// @Tags NotifyMsg
// @Summary 分页获取notifyMsg表列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取notifyMsg表列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /notifyMsg/getNotifyMsgList [get]
export const getNotifyMsgList = (params) => {
  return service({
    url: '/notifyMsg/getNotifyMsgList',
    method: 'get',
    params
  })
}

export const getAppNotify = () => {
  return service({
    url: '/notifyMsg/getAppNotify',
    method: 'get',
  })
}

export const setAppNotify = (data) => {
  return service({
    url: '/notifyMsg/setAppNotify',
    method: 'put',
    data,
  })
}

export const generateUrlLink = () => {
  return service({
    url: '/users/generateUrlLink',
    method: 'get'
  })
}

export const setGroupBuyNotify = (data) => {
  return service({
    url: '/notifyMsg/setGroupBuyNotify',
    method: 'post',
    data
  })
}