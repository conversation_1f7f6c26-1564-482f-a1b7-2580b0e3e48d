import service from '@/utils/request'

// @Tags SysMsg
// @Summary 创建sysMsg表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.SysMsg true "创建sysMsg表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"创建成功"}"
// @Router /sysMsg/createSysMsg [post]
export const createSysMsg = (data) => {
  return service({
    url: '/sysMsg/createSysMsg',
    method: 'post',
    data
  })
}

// @Tags SysMsg
// @Summary 删除sysMsg表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.SysMsg true "删除sysMsg表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /sysMsg/deleteSysMsg [delete]
export const deleteSysMsg = (data) => {
  return service({
    url: '/sysMsg/deleteSysMsg',
    method: 'delete',
    data
  })
}

// @Tags SysMsg
// @Summary 批量删除sysMsg表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除sysMsg表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /sysMsg/deleteSysMsg [delete]
export const deleteSysMsgByIds = (data) => {
  return service({
    url: '/sysMsg/deleteSysMsgByIds',
    method: 'delete',
    data
  })
}

// @Tags SysMsg
// @Summary 更新sysMsg表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.SysMsg true "更新sysMsg表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /sysMsg/updateSysMsg [put]
export const updateSysMsg = (data) => {
  return service({
    url: '/sysMsg/updateSysMsg',
    method: 'put',
    data
  })
}

// @Tags SysMsg
// @Summary 用id查询sysMsg表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query model.SysMsg true "用id查询sysMsg表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /sysMsg/findSysMsg [get]
export const findSysMsg = (params) => {
  return service({
    url: '/sysMsg/findSysMsg',
    method: 'get',
    params
  })
}

// @Tags SysMsg
// @Summary 分页获取sysMsg表列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取sysMsg表列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /sysMsg/getSysMsgList [get]
export const getSysMsgList = (params) => {
  return service({
    url: '/sysMsg/getSysMsgList',
    method: 'get',
    params
  })
}
