import service from '@/utils/request'

// @Tags SubOrders
// @Summary 创建subOrders表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.SubOrders true "创建subOrders表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"创建成功"}"
// @Router /subOrders/createSubOrders [post]
export const createSubOrders = (data) => {
  return service({
    url: '/subOrders/createSubOrders',
    method: 'post',
    data
  })
}

// @Tags SubOrders
// @Summary 删除subOrders表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.SubOrders true "删除subOrders表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /subOrders/deleteSubOrders [delete]
export const deleteSubOrders = (data) => {
  return service({
    url: '/subOrders/deleteSubOrders',
    method: 'delete',
    data
  })
}

// @Tags SubOrders
// @Summary 批量删除subOrders表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除subOrders表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /subOrders/deleteSubOrders [delete]
export const deleteSubOrdersByIds = (data) => {
  return service({
    url: '/subOrders/deleteSubOrdersByIds',
    method: 'delete',
    data
  })
}

// @Tags SubOrders
// @Summary 更新subOrders表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.SubOrders true "更新subOrders表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /subOrders/updateSubOrders [put]
export const updateSubOrders = (data) => {
  return service({
    url: '/subOrders/updateSubOrders',
    method: 'put',
    data
  })
}

// @Tags SubOrders
// @Summary 用id查询subOrders表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query model.SubOrders true "用id查询subOrders表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /subOrders/findSubOrders [get]
export const findSubOrders = (params) => {
  return service({
    url: '/subOrders/findSubOrders',
    method: 'get',
    params
  })
}

// @Tags SubOrders
// @Summary 分页获取subOrders表列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取subOrders表列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /subOrders/getSubOrdersList [get]
export const getSubOrdersList = (params) => {
  return service({
    url: '/subOrders/getSubOrdersList',
    method: 'get',
    params
  })
}
