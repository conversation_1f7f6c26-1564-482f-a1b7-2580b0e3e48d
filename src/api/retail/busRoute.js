import service from '@/utils/request'

// @Tags BusRoute
// @Summary 创建busRoute表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.BusRoute true "创建busRoute表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"创建成功"}"
// @Router /busRoute/createBusRoute [post]
export const createBusRoute = (data) => {
  return service({
    url: '/busRoute/createBusRoute',
    method: 'post',
    data
  })
}

// @Tags BusRoute
// @Summary 删除busRoute表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.BusRoute true "删除busRoute表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /busRoute/deleteBusRoute [delete]
export const deleteBusRoute = (data) => {
  return service({
    url: '/busRoute/deleteBusRoute',
    method: 'delete',
    data
  })
}

// @Tags BusRoute
// @Summary 批量删除busRoute表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除busRoute表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /busRoute/deleteBusRoute [delete]
export const deleteBusRouteByIds = (data) => {
  return service({
    url: '/busRoute/deleteBusRouteByIds',
    method: 'delete',
    data
  })
}

// @Tags BusRoute
// @Summary 更新busRoute表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.BusRoute true "更新busRoute表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /busRoute/updateBusRoute [put]
export const updateBusRoute = (data) => {
  return service({
    url: '/busRoute/updateBusRoute',
    method: 'put',
    data
  })
}

// @Tags BusRoute
// @Summary 用id查询busRoute表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query model.BusRoute true "用id查询busRoute表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /busRoute/findBusRoute [get]
export const findBusRoute = (params) => {
  return service({
    url: '/busRoute/findBusRoute',
    method: 'get',
    params
  })
}

// @Tags BusRoute
// @Summary 分页获取busRoute表列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取busRoute表列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /busRoute/getBusRouteList [get]
export const getBusRouteList = (params) => {
  return service({
    url: '/busRoute/getBusRouteList',
    method: 'get',
    params
  })
}
export const getRouteType = (params) => {
  return service({
    url: '/busRoute/getRouteType',
    method: 'get',
    params
  })
}
export const getArea = (params) => {
  return service({
    url: '/busRoute/getArea',
    method: 'get',
    params
  })
}
