import service from '@/utils/request'

// @Tags Bus
// @Summary 创建bus表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Bus true "创建bus表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"创建成功"}"
// @Router /bus/createBus [post]
export const createBus = (data) => {
  return service({
    url: '/bus/createBus',
    method: 'post',
    data
  })
}

// @Tags Bus
// @Summary 删除bus表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Bus true "删除bus表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /bus/deleteBus [delete]
export const deleteBus = (data) => {
  return service({
    url: '/bus/deleteBus',
    method: 'delete',
    data
  })
}

// @Tags Bus
// @Summary 批量删除bus表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除bus表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /bus/deleteBus [delete]
export const deleteBusByIds = (data) => {
  return service({
    url: '/bus/deleteBusByIds',
    method: 'delete',
    data
  })
}

// @Tags Bus
// @Summary 更新bus表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Bus true "更新bus表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /bus/updateBus [put]
export const updateBus = (data) => {
  return service({
    url: '/bus/updateBus',
    method: 'put',
    data
  })
}

// @Tags Bus
// @Summary 用id查询bus表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query model.Bus true "用id查询bus表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /bus/findBus [get]
export const findBus = (params) => {
  return service({
    url: '/bus/findBus',
    method: 'get',
    params
  })
}

// @Tags Bus
// @Summary 分页获取bus表列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取bus表列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /bus/getBusList [get]
export const getBusList = (params) => {
  return service({
    url: '/bus/getBusList',
    method: 'get',
    params
  })
}
