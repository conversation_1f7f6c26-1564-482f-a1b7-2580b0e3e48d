import service from '@/utils/request'

// @Tags SysUserArea
// @Summary 创建sysUserArea表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.SysUserArea true "创建sysUserArea表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"创建成功"}"
// @Router /sysUserArea/createSysUserArea [post]
export const createSysUserArea = (data) => {
  return service({
    url: '/sysUserArea/createSysUserArea',
    method: 'post',
    data
  })
}

// @Tags SysUserArea
// @Summary 删除sysUserArea表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.SysUserArea true "删除sysUserArea表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /sysUserArea/deleteSysUserArea [delete]
export const deleteSysUserArea = (data) => {
  return service({
    url: '/sysUserArea/deleteSysUserArea',
    method: 'delete',
    data
  })
}

// @Tags SysUserArea
// @Summary 批量删除sysUserArea表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除sysUserArea表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /sysUserArea/deleteSysUserArea [delete]
export const deleteSysUserAreaByIds = (data) => {
  return service({
    url: '/sysUserArea/deleteSysUserAreaByIds',
    method: 'delete',
    data
  })
}

// @Tags SysUserArea
// @Summary 更新sysUserArea表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.SysUserArea true "更新sysUserArea表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /sysUserArea/updateSysUserArea [put]
export const updateSysUserArea = (data) => {
  return service({
    url: '/sysUserArea/updateSysUserArea',
    method: 'put',
    data
  })
}

export const saveSysUserArea = (data) => {
  return service({
    url: '/sysUserArea/saveSysUserArea',
    method: 'put',
    data
  })
}

// @Tags SysUserArea
// @Summary 用id查询sysUserArea表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query model.SysUserArea true "用id查询sysUserArea表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /sysUserArea/findSysUserArea [get]
export const findSysUserArea = (params) => {
  return service({
    url: '/sysUserArea/findSysUserArea',
    method: 'get',
    params
  })
}

export const findSysUserAreaInfo = (params) => {
  return service({
    url: '/sysUserArea/findSysUserAreaInfo',
    method: 'get',
    params
  })
}

// @Tags SysUserArea
// @Summary 分页获取sysUserArea表列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取sysUserArea表列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /sysUserArea/getSysUserAreaList [get]
export const getSysUserAreaList = (params) => {
  return service({
    url: '/sysUserArea/getSysUserAreaList',
    method: 'get',
    params
  })
}
