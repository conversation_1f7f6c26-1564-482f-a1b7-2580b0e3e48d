import service from '@/utils/request'

// @Tags UserFeedback
// @Summary 创建userFeedback表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.UserFeedback true "创建userFeedback表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"创建成功"}"
// @Router /userFeedback/createUserFeedback [post]
export const createUserFeedback = (data) => {
  return service({
    url: '/userFeedback/createUserFeedback',
    method: 'post',
    data
  })
}

// @Tags UserFeedback
// @Summary 删除userFeedback表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.UserFeedback true "删除userFeedback表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /userFeedback/deleteUserFeedback [delete]
export const deleteUserFeedback = (data) => {
  return service({
    url: '/userFeedback/deleteUserFeedback',
    method: 'delete',
    data
  })
}

// @Tags UserFeedback
// @Summary 批量删除userFeedback表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除userFeedback表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /userFeedback/deleteUserFeedback [delete]
export const deleteUserFeedbackByIds = (data) => {
  return service({
    url: '/userFeedback/deleteUserFeedbackByIds',
    method: 'delete',
    data
  })
}

// @Tags UserFeedback
// @Summary 更新userFeedback表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.UserFeedback true "更新userFeedback表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /userFeedback/updateUserFeedback [put]
export const updateUserFeedback = (data) => {
  return service({
    url: '/userFeedback/updateUserFeedback',
    method: 'put',
    data
  })
}

// @Tags UserFeedback
// @Summary 用id查询userFeedback表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query model.UserFeedback true "用id查询userFeedback表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /userFeedback/findUserFeedback [get]
export const findUserFeedback = (params) => {
  return service({
    url: '/userFeedback/findUserFeedback',
    method: 'get',
    params
  })
}

// @Tags UserFeedback
// @Summary 分页获取userFeedback表列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取userFeedback表列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /userFeedback/getUserFeedbackList [get]
export const getUserFeedbackList = (params) => {
  return service({
    url: '/userFeedback/getUserFeedbackList',
    method: 'get',
    params
  })
}

export const getBusinessTypeMapInfo = (params) => {
  return service({
    url: '/userFeedback/businessTypeMap',
    method: 'get',
    params
  })
}
