import service from '@/utils/request'

// @Tags GpsDevice
// @Summary 创建gpsDevice表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.GpsDevice true "创建gpsDevice表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"创建成功"}"
// @Router /gpsDevice/createGpsDevice [post]
export const createGpsDevice = (data) => {
  return service({
    url: '/gpsDevice/createGpsDevice',
    method: 'post',
    data
  })
}

// @Tags GpsDevice
// @Summary 删除gpsDevice表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.GpsDevice true "删除gpsDevice表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /gpsDevice/deleteGpsDevice [delete]
export const deleteGpsDevice = (data) => {
  return service({
    url: '/gpsDevice/deleteGpsDevice',
    method: 'delete',
    data
  })
}

// @Tags GpsDevice
// @Summary 批量删除gpsDevice表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除gpsDevice表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /gpsDevice/deleteGpsDevice [delete]
export const deleteGpsDeviceByIds = (data) => {
  return service({
    url: '/gpsDevice/deleteGpsDeviceByIds',
    method: 'delete',
    data
  })
}

// @Tags GpsDevice
// @Summary 更新gpsDevice表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.GpsDevice true "更新gpsDevice表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /gpsDevice/updateGpsDevice [put]
export const updateGpsDevice = (data) => {
  return service({
    url: '/gpsDevice/updateGpsDevice',
    method: 'put',
    data
  })
}

// @Tags GpsDevice
// @Summary 用id查询gpsDevice表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query model.GpsDevice true "用id查询gpsDevice表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /gpsDevice/findGpsDevice [get]
export const findGpsDevice = (params) => {
  return service({
    url: '/gpsDevice/findGpsDevice',
    method: 'get',
    params
  })
}

// @Tags GpsDevice
// @Summary 分页获取gpsDevice表列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取gpsDevice表列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /gpsDevice/getGpsDeviceList [get]
export const getGpsDeviceList = (params) => {
  return service({
    url: '/gpsDevice/getGpsDeviceList',
    method: 'get',
    params
  })
}
