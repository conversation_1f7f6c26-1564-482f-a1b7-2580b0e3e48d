import service from '@/utils/request'

// @Tags OrderEvaluate
// @Summary 创建orderEvaluate表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.OrderEvaluate true "创建orderEvaluate表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"创建成功"}"
// @Router /orderEvaluate/createOrderEvaluate [post]
export const createOrderEvaluate = (data) => {
  return service({
    url: '/orderEvaluate/createOrderEvaluate',
    method: 'post',
    data
  })
}

// @Tags OrderEvaluate
// @Summary 删除orderEvaluate表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.OrderEvaluate true "删除orderEvaluate表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /orderEvaluate/deleteOrderEvaluate [delete]
export const deleteOrderEvaluate = (data) => {
  return service({
    url: '/orderEvaluate/deleteOrderEvaluate',
    method: 'delete',
    data
  })
}

// @Tags OrderEvaluate
// @Summary 批量删除orderEvaluate表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除orderEvaluate表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /orderEvaluate/deleteOrderEvaluate [delete]
export const deleteOrderEvaluateByIds = (data) => {
  return service({
    url: '/orderEvaluate/deleteOrderEvaluateByIds',
    method: 'delete',
    data
  })
}

// @Tags OrderEvaluate
// @Summary 更新orderEvaluate表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.OrderEvaluate true "更新orderEvaluate表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /orderEvaluate/updateOrderEvaluate [put]
export const updateOrderEvaluate = (data) => {
  return service({
    url: '/orderEvaluate/updateOrderEvaluate',
    method: 'put',
    data
  })
}

// @Tags OrderEvaluate
// @Summary 用id查询orderEvaluate表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query model.OrderEvaluate true "用id查询orderEvaluate表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /orderEvaluate/findOrderEvaluate [get]
export const findOrderEvaluate = (params) => {
  return service({
    url: '/orderEvaluate/findOrderEvaluate',
    method: 'get',
    params
  })
}

// @Tags OrderEvaluate
// @Summary 分页获取orderEvaluate表列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取orderEvaluate表列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /orderEvaluate/getOrderEvaluateList [get]
export const getOrderEvaluateList = (params) => {
  return service({
    url: '/orderEvaluate/getOrderEvaluateList',
    method: 'get',
    params
  })
}
