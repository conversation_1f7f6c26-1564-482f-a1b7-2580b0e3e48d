<template>
  <div>
    <el-upload
      drag
      :action="`${path}/users/uploadFile?file_type=${fileType}`"
      :file-list="upFileList"
      :headers="{ 'x-token': userStore.token }"
      :limit="1"
      :on-exceed="fileExceed"
      :on-remove="fileRemove"
      :on-success="uploadSuccess"
    >
      <el-icon class="el-icon--upload">
        <upload-filled/>
      </el-icon>
      <div class="el-upload__text">拖拽文件或者点击上传</div>
      <!--<template #tip><div class="el-upload__tip">文件大小不要超过10M</div></template>-->
    </el-upload>
  </div>
</template>

<script setup>
import {ref, onBeforeMount} from 'vue'
import {ElMessage} from 'element-plus'
import {useUserStore} from '@/pinia/modules/user'
import {UploadFilled} from '@element-plus/icons-vue'
import {getOssFileUrl} from "@/utils/format";

defineOptions({name: 'UploadFile'})

const userStore = useUserStore()

const props = defineProps({
  fileUrl: {
    type: String,
    default: '',
  },
  limit: {
    type: Number,
    default: 1,
  },
  fileType: {
    type: Number,
    default: 1,
    required: true
  },
  uploadSuccess: {
    type: Function,
    required: true
  },
  fileRemove: {
    type: Function,
    required: true
  },
})

const path = ref(import.meta.env.VITE_BASE_API)
const upFileList = ref([])

const fileExceed = () => {
  ElMessage.error('请先删除,然后在上传')
}

onBeforeMount(() => {
  if (props.fileUrl) {
    let fileUrlFull = getOssFileUrl(props.fileUrl)
    upFileList.value = [{
      name: props.fileUrl,
      url: fileUrlFull,
    }]
  }
})

</script>

<style lang="scss" scoped>
.image-uploader {
  border: 1px dashed #d9d9d9;
  width: 180px;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.image-uploader {
  border-color: #409eff;
}

.image-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}

.image {
  width: 178px;
  height: 178px;
  display: block;
}
</style>
