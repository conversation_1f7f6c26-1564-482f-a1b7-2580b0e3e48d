<template>
  <div>
    <el-upload
      drag
      :action="`${path}/users/uploadFile?file_type=${fileType}`"
      :file-list="upFileList"
      :headers="{ 'x-token': userStore.token }"
      :limit="1"
      :on-exceed="fileExceed"
      :on-remove="fileRemove"
      :on-preview="filePreview"
      :on-success="uploadSuccess"
      accept="image/*"
      list-type="picture-card"
    >
      <el-icon class="avatar-uploader-icon">
        <Plus/>
      </el-icon>
    </el-upload>
    <el-dialog v-model="dialogVisible" title="查看" class="w-[90%] md:w-[50%]">
      <el-image
        :preview-src-list="[previewImgUrl]"
        :src="previewImgUrl"
        fit="cover"
        style="width: 100%"
      />
    </el-dialog>
  </div>
</template>

<script setup>
import {ref, onMounted, onBeforeMount} from 'vue'
import {ElMessage} from 'element-plus'
import {useUserStore} from '@/pinia/modules/user'
import {Plus} from "@element-plus/icons-vue";
import {getOssFileUrl} from "@/utils/format";

defineOptions({name: 'UploadImg'})

const userStore = useUserStore()

const props = defineProps({
  imgUrl: {
    type: String,
    default: '',
  },
  fileType: {
    type: Number,
    default: 1,
    required: true
  },
  uploadSuccess: {
    type: Function,
    required: true
  },
  fileRemove: {
    type: Function,
    required: true
  },
})

const path = ref(import.meta.env.VITE_BASE_API)
const previewImgUrl = ref(null)
const upFileList = ref([])
const dialogVisible = ref(false)


const filePreview = (uploadFile) => {
  previewImgUrl.value = uploadFile.url
  dialogVisible.value = true
}

const fileExceed = () => {
  ElMessage.error('请先删除,然后在上传')
}

onBeforeMount(() => {
  if (props.imgUrl) {
    let img_full = getOssFileUrl(props.imgUrl)
    upFileList.value = [{
      name: props.imgUrl,
      url: img_full,
    }]
  }
})

</script>

<style lang="scss" scoped>
.image-uploader {
  border: 1px dashed #d9d9d9;
  width: 180px;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.image-uploader {
  border-color: #409eff;
}

.image-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}

.image {
  width: 178px;
  height: 178px;
  display: block;
}
</style>
