<template>
  <div class="main">
    <el-upload
      drag
      :action="`${path}/users/uploadFile?file_type=${fileType}`"
      :file-list="upFileList"
      :headers="{ 'x-token': userStore.token }"
      :limit="limit"
      :on-exceed="fileExceed"
      :on-preview="filePreview"
      :on-remove="fileRemove"
      :on-success="fileUploadSuccess"
      :show-file-list="false"
      accept="image/*"
      list-type="picture-card"
    >
      <el-icon class="avatar-uploader-icon">
        <Plus />
      </el-icon>
    </el-upload>

    <draggable
      v-model="upFileList"
      class="draggable"
      item-key="uid"
      animation="300"
      @start="onStart"
      @end="onEnd"
    >
      <template #item="{ element }">
        <div class="elementFlex">
          <img :src="element.url" />
          <div class="content-wrap">
            <div class="content">
              <el-icon class="plus" @click="filePreview(element)"
                ><CirclePlus
              /></el-icon>
              <el-icon class="delete" @click="deleteItem(element)"
                ><Delete
              /></el-icon>
            </div>
          </div>
        </div>
      </template>
    </draggable>

    <el-dialog v-model="dialogVisible" title="查看">
      <el-image
        :preview-src-list="[previewImgUrl]"
        :src="previewImgUrl"
        fit="cover"
        style="width: 300px"
      />
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeMount, defineEmits } from "vue";
import { ElMessage } from "element-plus";
import { useUserStore } from "@/pinia/modules/user";
import { Plus } from "@element-plus/icons-vue";
import { getOssFileUrl, getOssFileUrlList } from "@/utils/format";
import draggable from "vuedraggable";

defineOptions({ name: "UploadImgList" });

const userStore = useUserStore();
const deleteArr = ref([]);

const emit = defineEmits(["updateImageList"]);

const props = defineProps({
  imgUrlList: {
    type: Array,
    default: [],
  },
  fileType: {
    type: Number,
    default: 1,
    required: true,
  },
  limit: {
    type: Number,
    default: 1,
    required: true,
  },
  fileRemove: {
    type: Function,
    required:true
  },
  fileUploadSuccess: {
    type: Function,
    required:true
  },
});

const path = ref(import.meta.env.VITE_BASE_API);
const previewImgUrl = ref(null);
const upFileList = ref([]);
const dialogVisible = ref(false);

const filePreview = (uploadFile) => {
  previewImgUrl.value = uploadFile.url;
  dialogVisible.value = true;
};

const fileExceed = () => {
  ElMessage.error("已超出数量限制,请先删除,然后在上传");
};

const onStart = (val) => {
};

const onEnd = (e) => {
  emit('updateImageList',upFileList.value)
};

const deleteItem = (val) => {
  let imgKey = val.name;

  upFileList.value.forEach((item) => {
    deleteArr.value.push(item.name);
  });
  let index = deleteArr.value.indexOf(imgKey);
  if (index > -1) {
    upFileList.value.splice(index, 1);
  }
  emit("updateImageList", upFileList.value);
};

// const fileUploadSuccess = (response, uploadFile) => {
//   if (response.code === 0) {
//     upFileList.value.push({
//       name: response.data.key,
//       url: response.data.url,
//     });
//     // emit("updateImageList", upFileList.value);
//   }
// };

onBeforeMount(() => {
  if (props.imgUrlList && props.imgUrlList.length > 0) {
    let img_full_list = getOssFileUrlList(props.imgUrlList);
    props.imgUrlList.forEach((item, index) => {
      upFileList.value.push({
        name: item,
        url: img_full_list[index],
      });
    });
  }
});
</script>

<style lang="scss" scoped>
.main{
  display: flex;
}
.image-uploader {
  border: 1px dashed #d9d9d9;
  width: 180px;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.image-uploader {
  border-color: #409eff;
}

.image-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}

.image {
  width: 178px;
  height: 178px;
  display: block;
}

.draggable {
  // height: 150px;
  width: 100%;
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  .elementFlex{
    width: 150px;
    height: 150px;
    margin-right: 10px;
    margin-bottom: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    img{
      flex: 1;
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
    .content-wrap{
      width: 100%;
      height: 20px;
      padding-left: 40%;
      box-sizing: border-box;
      .delete{
        margin-left: 5px;
      }
    }
  }
}

</style>
