@import '@/style/main.scss';

#app {
    .el-button {
        font-weight: 400;
        border-radius: 2px;
    }
}

::-webkit-scrollbar {
    @apply hidden;
}


.gva-search-box {
    @apply p-6 pb-0.5 bg-white rounded mb-3;
}

.gva-form-box {
    @apply p-6 bg-white rounded;
}

.gva-pagination {
    @apply flex justify-end;
    .el-pagination__editor {
        .el-input__inner {
            @apply h-8;
        }
    }

    .is-active {
        @apply rounded text-white;
        background: var(--el-color-primary);
        color: #ffffff !important;
    }
}

