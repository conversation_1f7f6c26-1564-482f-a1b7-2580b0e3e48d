/* Document
   ========================================================================== */


/**
 * 1. Correct the line height in all browsers.
 * 2. Prevent adjustments of font size after orientation changes in iOS.
 */

@import '@/style/iconfont.css';
html {
    line-height: 1.15;
    /* 1 */
    -webkit-text-size-adjust: 100%;
    /* 2 */
}


/* Sections
     ========================================================================== */


/**
   * Remove the margin in all browsers.
   */

body {
    margin: 0;
}


/**
   * Render the `main` element consistently in IE.
   */

main {
    display: block;
}


/**
   * Correct the font size and margin on `h1` elements within `section` and
   * `article` contexts in Chrome, Firefox, and Safari.
   */

h1 {
    font-size: 2em;
    margin: 0.67em 0;
}


/* Grouping content
     ========================================================================== */


/**
   * 1. Add the correct box sizing in Firefox.
   * 2. Show the overflow in Edge and IE.
   */

hr {
    box-sizing: content-box;
    /* 1 */
    height: 0;
    /* 1 */
    overflow: visible;
    /* 2 */
}


/**
   * 1. Correct the inheritance and scaling of font size in all browsers.
   * 2. Correct the odd `em` font sizing in all browsers.
   */

pre {
    font-family: monospace, monospace;
    /* 1 */
    font-size: 1em;
    /* 2 */
}


/* Text-level semantics
     ========================================================================== */


/**
   * Remove the gray background on active links in IE 10.
   */

a {
    background-color: transparent;
}


/**
   * 1. Remove the bottom border in Chrome 57-
   * 2. Add the correct text decoration in Chrome, Edge, IE, Opera, and Safari.
   */

abbr[title] {
    border-bottom: none;
    /* 1 */
    text-decoration: underline;
    /* 2 */
    text-decoration: underline dotted;
    /* 2 */
}


/**
   * Add the correct font weight in Chrome, Edge, and Safari.
   */

b,
strong {
    font-weight: bolder;
}


/**
   * 1. Correct the inheritance and scaling of font size in all browsers.
   * 2. Correct the odd `em` font sizing in all browsers.
   */

code,
kbd,
samp {
    font-family: monospace, monospace;
    /* 1 */
    font-size: 1em;
    /* 2 */
}


/**
   * Add the correct font size in all browsers.
   */

small {
    font-size: 80%;
}


/**
   * Prevent `sub` and `sup` elements from affecting the line height in
   * all browsers.
   */

sub,
sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline;
}

sub {
    bottom: -0.25em;
}

sup {
    top: -0.5em;
}


/* Embedded content
     ========================================================================== */


/**
   * Remove the border on images inside links in IE 10.
   */

img {
    border-style: none;
}


/* Forms
     ========================================================================== */


/**
   * 1. Change the font styles in all browsers.
   * 2. Remove the margin in Firefox and Safari.
   */

button,
input,
optgroup,
select,
textarea {
    font-family: inherit;
    /* 1 */
    font-size: 100%;
    /* 1 */
    line-height: 1.15;
    /* 1 */
    margin: 0;
    /* 2 */
}


/**
   * Show the overflow in IE.
   * 1. Show the overflow in Edge.
   */

button,
input {
    /* 1 */
    overflow: visible;
}


/**
   * Remove the inheritance of text transform in Edge, Firefox, and IE.
   * 1. Remove the inheritance of text transform in Firefox.
   */

button,
select {
    /* 1 */
    text-transform: none;
}


/**
   * Correct the inability to style clickable types in iOS and Safari.
   */

button,
[type="button"],
[type="reset"],
[type="submit"] {
    -webkit-appearance: button;
}


/**
   * Remove the inner border and padding in Firefox.
   */

button::-moz-focus-inner,
[type="button"]::-moz-focus-inner,
[type="reset"]::-moz-focus-inner,
[type="submit"]::-moz-focus-inner {
    border-style: none;
    padding: 0;
}


/**
   * Restore the focus styles unset by the previous rule.
   */

button:-moz-focusring,
[type="button"]:-moz-focusring,
[type="reset"]:-moz-focusring,
[type="submit"]:-moz-focusring {
    outline: 1px dotted ButtonText;
}


/**
   * Correct the padding in Firefox.
   */

fieldset {
    padding: 0.35em 0.75em 0.625em;
}


/**
   * 1. Correct the text wrapping in Edge and IE.
   * 2. Correct the color inheritance from `fieldset` elements in IE.
   * 3. Remove the padding so developers are not caught out when they zero out
   *    `fieldset` elements in all browsers.
   */

legend {
    box-sizing: border-box;
    /* 1 */
    color: inherit;
    /* 2 */
    display: table;
    /* 1 */
    max-width: 100%;
    /* 1 */
    padding: 0;
    /* 3 */
    white-space: normal;
    /* 1 */
}


/**
   * Add the correct vertical alignment in Chrome, Firefox, and Opera.
   */

progress {
    vertical-align: baseline;
}


/**
   * Remove the default vertical scrollbar in IE 10+.
   */

textarea {
    overflow: auto;
}


/**
   * 1. Add the correct box sizing in IE 10.
   * 2. Remove the padding in IE 10.
   */

[type="checkbox"],
[type="radio"] {
    box-sizing: border-box;
    /* 1 */
    padding: 0;
    /* 2 */
}


/**
   * Correct the cursor style of increment and decrement buttons in Chrome.
   */

[type="number"]::-webkit-inner-spin-button,
[type="number"]::-webkit-outer-spin-button {
    height: auto;
}


/**
   * 1. Correct the odd appearance in Chrome and Safari.
   * 2. Correct the outline style in Safari.
   */

[type="search"] {
    -webkit-appearance: textfield;
    /* 1 */
    outline-offset: -2px;
    /* 2 */
}


/**
   * Remove the inner padding in Chrome and Safari on macOS.
   */

[type="search"]::-webkit-search-decoration {
    -webkit-appearance: none;
}


/**
   * 1. Correct the inability to style clickable types in iOS and Safari.
   * 2. Change font properties to `inherit` in Safari.
   */

::-webkit-file-upload-button {
    -webkit-appearance: button;
    /* 1 */
    font: inherit;
    /* 2 */
}


/* Interactive
     ========================================================================== */


/*
   * Add the correct display in Edge, IE 10+, and Firefox.
   */

details {
    display: block;
}


/*
   * Add the correct display in all browsers.
   */

summary {
    display: list-item;
}


/* Misc
     ========================================================================== */


/**
   * Add the correct display in IE 10+.
   */

template {
    display: none;
}


/**
   * Add the correct display in IE 10.
   */

[hidden] {
    display: none;
}

HTML,
body,
div,
ul,
ol,
dl,
li,
dt,
dd,
p,
blockquote,
pre,
form,
fieldset,
table,
th,
td {
    border: none;
    font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
    font-size: 14px;
    margin: 0px;
    padding: 0px;
}

html,
body {
    height: 100%;
    width: 100%;
}

address,
caption,
cite,
code,
th,
var {
    font-style: normal;
    font-weight: normal;
}

a {
    text-decoration: none;
}

input::-ms-clear {
    display: none;
}

input::-ms-reveal {
    display: none;
}

input {
    -webkit-appearance: none;
    margin: 0;
    outline: none;
    padding: 0;
}

input::-webkit-input-placeholder {
    color: #ccc;
}

input::-ms-input-placeholder {
    color: #ccc;
}

input::-moz-placeholder {
    color: #ccc;
}

input[type=submit],
input[type=button] {
    cursor: pointer;
}

button[disabled],
input[disabled] {
    cursor: default;
}

img {
    border: none;
}

ul,
ol,
li {
    list-style-type: none;
}

// 导航
#app {
    .el-container {
        @apply relative h-full w-full;
    }
    .el-container.mobile.openside {
        @apply fixed top-0;
    }
    .gva-aside {
        @apply fixed top-0 left-0 z-[1001] overflow-hidden;
        .el-menu {
            @apply border-r-0;
        }
    }
    .aside {
        .el-menu--collapse {
            >.el-menu-item {
                display: flex;
                justify-content: center;
            }
        }
        .el-sub-menu {
            .el-menu {
                .is-active {
                    // 关闭三级菜单二级菜单样式
                    ul {
                        border: none;
                    }
                }
                // 关闭三级菜单二级菜单样式
                .is-active.is-opened {
                    ul {
                        border: none;
                    }
                }
            }
        }
    }
    .hideside {
        .aside {
            @apply w-[54px]
        }
    }

    .mobile {
        .gva-aside {
            @apply w-[54px];
        }
    }

    .hideside {
        .main-cont.el-main {
            @apply ml-[54px];
        }
    }
    .mobile {
        .main-cont.el-main {
            @apply ml-0;
        }
    }
}

//   layout

.admin-box {
    @apply min-h-[calc(100vh-200px)] mt-28 mx-1;
    .el-table {
        th {
            @apply px-0 py-2;
            .cell {
                @apply leading-[40px] text-gray-700;
            }
        }
        td {
            @apply px-0 py-2;
            .cell {
                @apply leading-[40px] text-gray-600;
            }
        }
        .is-leaf {
            @apply border-b border-t-0 border-l-0 border-solid border-gray-50;
            border-right:var(--el-table-border);
            background: #F7FBFF !important;
        }
    }
}

// table
.el-pagination {
    @apply mt-8;
    .btn-prev,
    .btn-next {
        @apply border border-solid border-gray-300 rounded;
    }
    .el-pager {
        li {
            @apply border border-solid border-gray-300 rounded text-gray-600 text-sm mx-1;
        }
    }
}


.el-container.layout-cont {
    .header-cont {
        @apply px-4 h-16 bg-white;
    }


    .main-cont {
        @apply h-screen overflow-visible;
        &.el-main {
            @apply min-h-full ml-[220px] bg-main p-0 overflow-auto;
        }

        .breadcrumb {
            @apply h-16 flex items-center p-0 ml-12 text-lg;
            .el-breadcrumb__item {
                .el-breadcrumb__inner {
                    @apply text-gray-600;
                }
            }
            .el-breadcrumb__item:nth-last-child(1) {
                .el-breadcrumb__inner {
                    @apply text-gray-600;
                }
            }
        }

        .router-history {
            @apply bg-white p-0 border-t border-l-0 border-r-0 border-b-0 border-solid border-gray-100;
            .el-tabs__header {
                @apply m-0;
                .el-tabs__item{
                    @apply border-solid border-r border-t-0 border-gray-100 border-b-0 border-l-0;
                }
                .el-tabs__item.is-active {
                    @apply bg-blue-500 bg-opacity-5;
                }
                .el-tabs__nav {
                    @apply border-0;
                }
            }
        }

        .aside {
            @apply overflow-auto;
        }
        .el-menu-vertical {
            @apply h-[calc(100vh-60px)];
            &:not(.el-menu--collapse) {
                @apply w-[220px];
            }
        }
        .el-menu--collapse {
            @apply w-[54px];
            li {
                .el-tooltip,
                .el-sub-menu__title {
                    @apply px-4;
                }
            }
        }
    }
}

.el-dropdown {
    @apply overflow-hidden
}

.gva-table-box {
    @apply p-6 bg-white rounded;
}

.gva-btn-list {
    @apply mb-3 flex gap-3 items-center;
}


#nprogress .bar {
    background: #29d !important;
}
