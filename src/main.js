import 'element-plus/es/components/message/style/css'
import 'element-plus/es/components/loading/style/css'
import 'element-plus/es/components/notification/style/css'
import 'element-plus/es/components/message-box/style/css'
import './style/element_visiable.scss'
import {createApp} from 'vue'
import ElementPlus from 'element-plus'
// 引入gin-vue-admin前端初始化相关内容
import './core/gin-vue-admin'
// 引入封装的router
import router from '@/router/index'
import '@/permission'
import run from '@/core/gin-vue-admin.js'
import auth from '@/directive/auth'
import {store} from '@/pinia'
import App from './App.vue'
import {initDom} from './utils/positionToCode'
/**
 * @description 导入加载进度条，防止首屏加载时间过长，用户等待
 *
 * */
import Nprogress from 'nprogress'
import 'nprogress/nprogress.css'
// 图片上传组件
// import UploadImg from "@/components/upload/uploadImg.vue"

initDom()

Nprogress.configure({showSpinner: false, ease: 'ease', speed: 500})
Nprogress.start()

/**
 * 无需在这块结束，会在路由中间件中结束此块内容
 * */


const app = createApp(App)
app.config.productionTip = false
// 全局组件挂载
// app.component('UploadImg', UploadImg)
app
  .use(run)
  .use(store)
  .use(auth)
  .use(router)
  .mount('#app')

export default app
