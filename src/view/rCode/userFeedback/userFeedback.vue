<template>
  <div>
    <el-card shadow="hover">
      <el-form :inline="true" :model="searchInfo" @keyup.enter="onSubmit">
        <el-form-item label="时间" prop="create_range">
          <el-date-picker v-model="searchInfo.create_range" type="datetimerange" value-format="YYYY-MM-DDTHH:mm:ssZ" />
        </el-form-item>
        <el-form-item label="区域" prop="region">
          <el-select v-model="searchInfo.region" clearable placeholder="请选择">
            <el-option v-for="item in userStore.areasList" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="业务类型" prop="business">
          <el-select v-model="searchInfo.business" clearable placeholder="请选择">
            <el-option v-for="item in userStore.businessTypeList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="用户ID" prop="user_id">
          <el-input v-model.number="searchInfo.user_id" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="名字" prop="name">
          <el-input v-model="searchInfo.name" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="searchInfo.phone" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="search" @click="onSubmit">查询</el-button>
          <el-button icon="refresh" @click="onReset">重置</el-button>
          <el-button :disabled="total < 1" icon="Download" type="success" @click="onExportNew">导出</el-button>
        </el-form-item>
      </el-form>
      <el-table ref="tableRef" :data="tableData" row-key="id" border @sort-change="sortChange" show-overflow-tooltip>
        <el-table-column align="center" label="ID" prop="id" :width="90" fixed="left" sortable="custom" />
        <el-table-column align="center" label="创建时间" prop="created_at_str" :width="160" />
        <el-table-column align="center" label="用户ID" prop="user_id" :width="120" sortable="custom" />
        <el-table-column align="center" label="名字" prop="name" :min-width="120" sortable="custom" />
        <el-table-column align="center" label="手机号" prop="phone" :min-width="120" />
        <el-table-column align="center" label="区域" prop="region" :min-width="120" sortable="custom">
          <template #default="scope">
            <span>{{ userStore.GetAreaName(scope.row.region) }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="业务类型" prop="business" :min-width="120" sortable="custom">
          <template #default="scope">
            <span>{{ userStore.GetBusinessTypeName(scope.row.business) }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="反馈内容" prop="feedback_content" :width="180" />
        <el-table-column align="center" label="回复内容" prop="reply_content" :width="180" />
        <el-table-column :width="120" align="center" label="反馈图片数量">
          <template #default="scope">
            <div>{{ scope.row.feedback_image?.length || 0 }}</div>
          </template>
        </el-table-column>
        <el-table-column align="center" label="评分" prop="star" :min-width="120" sortable="custom" />
        <el-table-column align="center" label="操作" :width="280" fixed="right">
          <template #default="scope">
            <el-button type="primary" @click="viewFunc(scope.row)">查看详情</el-button>
            <!-- <el-button type="primary" @click="chatDialogVisible = true">回复</el-button> -->
            <el-button type="danger" @click="deleteRow(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="page"
        :layout="PageLayout"
        :page-size="pageSize"
        :page-sizes="PageSizes"
        :total="total"
        @current-change="pageChange"
        @size-change="pageSizeChange"
      />
    </el-card>
    <el-dialog v-model="dialogShow" :before-close="closeDialog" :title="dTitle" top="10px" destroy-on-close>
      <el-form ref="elFormRef" :model="formData" :rules="formDataRule" label-position="right" label-width="80px">
        <el-form-item label="用户ID" prop="user_id">
          <el-input-number :disabled="dType === 'view'" v-model="formData.user_id" style="width: 100%" />
        </el-form-item>
        <el-form-item label="名字" prop="name">
          <el-input :disabled="dType === 'view'" v-model="formData.name" :maxlength="32" show-word-limit clearable />
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input :disabled="dType === 'view'" v-model="formData.phone" :maxlength="32" show-word-limit clearable />
        </el-form-item>
        <el-form-item label="区域" prop="region">
          <el-select :disabled="dType === 'view'" v-model="formData.region" style="width: 100%">
            <el-option v-for="item in userStore.areasList" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="业务类型" prop="business">
          <el-select :disabled="dType === 'view'" v-model="formData.business" style="width: 100%">
            <el-option v-for="item in userStore.businessTypeList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="反馈图片" prop="feedback_image">
          <UploadImgList
            :disabled="dType === 'view'"
            :file-remove="fileRemove"
            :img-url-list="formData.feedback_image"
            :upload-success="fileUploadSuccess"
            :file-type="FileTypeFeedback"
            :limit="10"
          />
        </el-form-item>
        <el-form-item label="反馈内容" prop="feedback_content">
          <el-input :disabled="dType === 'view'" :autosize="{ minRows: 3, maxRows: 5 }" type="textarea" v-model="formData.feedback_content" />
        </el-form-item>
        <el-form-item label="评分" prop="star">
          <el-rate :disabled="dType === 'view'" v-model="formData.star" allow-half />
        </el-form-item>
        <el-divider>
          <span style="color: #f56c6c">回复区</span>
        </el-divider>

        <el-form-item label="回复" prop="reply_content">
          <el-input :disabled="!showSubmitBtn" v-model="formData.reply_content" :rows="4" type="textarea" placeholder="请输入回复内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div v-if="showSubmitBtn" class="dialog-footer">
          <el-button @click="closeDialog">取 消</el-button>
          <el-button type="primary" @click="toReply">确 定</el-button>
        </div>
      </template>

      <el-dialog v-model="imgDialogVisible" title="查看" append-to-body>
        <el-image :preview-src-list="[previewImgUrl]" :src="previewImgUrl" fit="cover" style="width: 300px" />
      </el-dialog>
    </el-dialog>
    <!-- 
    <el-dialog v-model="chatDialogVisible" title="反馈回复" lock-scroll append-to-body>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="chatDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="toReply">确 定</el-button>
        </div>
      </template>
    </el-dialog> -->
  </div>
</template>

<script setup>
import { createUserFeedback, deleteUserFeedback, findUserFeedback, getUserFeedbackList, updateUserFeedback } from '@/api/retail/userFeedback'
import { formatDate, getOssFileUrl } from '@/utils/format'
import { ElMessage, ElMessageBox } from 'element-plus'
import { onBeforeMount, reactive, ref } from 'vue'
import { FileTypeFeedback, PageLayout, PageSizes } from '@/utils/const'
import { useRoute, useRouter } from 'vue-router'
import { useUserStore } from '@/pinia/modules/user'
import UploadImgList from '@/components/upload/uploadImgList.vue'
import * as XLSX from 'xlsx'
import FileSaver from 'file-saver'
import ExcelJS from 'exceljs'

const userStore = useUserStore()
const router = useRouter()
const route = useRoute()
const path = ref(import.meta.env.VITE_BASE_API)
const tableRef = ref(null)
const upFileList = ref([])
const imgDialogVisible = ref(false)
const previewImgUrl = ref(null)
const dialogShow = ref(false)
const dTitle = ref('')
const dType = ref('')
const elFormRef = ref()
const page = ref(1)
const total = ref(0)
const pageSize = ref(100)
const tableData = ref([])
const searchInfo = ref({})
const formData = ref({ user_id: 0, name: '', phone: '', region: 0, business: 0, feedback_content: '', star: 0 })
const formDataRule = reactive({})

const showSubmitBtn = ref(true)

const chatDialogVisible = ref(false)
const msgValue = ref('')
const toReply = async () => {
  if (!formData.value.reply_content) {
    ElMessage({ type: 'error', message: '请输入回复内容' })
    return
  }
  const res = await updateUserFeedback(formData.value)
  if (res.code === 0) {
    ElMessage({ type: 'success', message: '回复成功' })
    dialogShow.value = false
    getTableData()
  }
}

const convertImgToBase64 = (imgUrl) => {
  return new Promise((resove, reject) => {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    const img = new Image()
    img.crossOrigin = 'Anonymous'
    img.onload = function () {
      canvas.height = img.height
      canvas.width = img.width
      ctx.drawImage(img, 0, 0, img.width, img.height)
      var dataURL = canvas.toDataURL('image/png')
      resove(dataURL)
    }
    img.onerror = function () {
      ElMessage.error(`图片加载异常,地址${imgUrl}`)
      reject(new Error('图片流异常'))
    }
    img.src = imgUrl
  })
}

const onExportNew = async () => {
  const workbook = new ExcelJS.Workbook()
  const sheet = workbook.addWorksheet('用户反馈')
  sheet.columns = [
    { header: 'ID', key: 'id' },
    { header: '创建时间', key: 'created_at_str' },
    { header: '用户ID', key: 'user_id' },
    { header: '名字', key: 'name' },
    { header: '手机号', key: 'phone' },
    { header: '区域', key: 'regionName' },
    { header: '业务类型', key: 'businessName' },
    { header: '反馈内容', key: 'feedback_content' },
    { header: '评分', key: 'star' },
    { header: '反馈图片', key: 'feedback_images' },
  ]
  // 批量设置
  // sheet.addRows(tableData.value)
  for (let i = 0; i < tableData.value.length; i++) {
    let rowIndex = i + 2
    let item = tableData.value[i]
    item.regionName = userStore.GetAreaName(item.region)
    item.businessName = userStore.GetBusinessTypeName(item.business)
    sheet.addRow(item)
    if (item.feedback_image.length > 0) {
      let colIndex = 'J'
      for (let j = 0; j < item.feedback_image.length; j++) {
        let imgItem = item.feedback_image[j]
        let img_full = getOssFileUrl(imgItem)
        let imgB64 = await convertImgToBase64(img_full)
        const imageId = workbook.addImage({
          base64: imgB64,
          extension: 'png',
        })
        let iRange = `${colIndex}${rowIndex}:${colIndex}${rowIndex}`
        sheet.addImage(imageId, iRange)
        colIndex = String.fromCharCode(colIndex.charCodeAt() + 1)
      }
    }
  }

  // 生成 Excel 文件并保存
  const buffer = await workbook.xlsx.writeBuffer()
  try {
    FileSaver.saveAs(
      new Blob([buffer], {
        type: 'application/octet-stream',
      }),
      `用户反馈${new Date().valueOf()}.xlsx`
    )
  } catch (e) {
    console.log(e)
  }
}

const onExport = () => {
  const wb = XLSX.utils.table_to_book(tableRef.value.$el, { raw: true })
  const wbout = XLSX.write(wb, {
    bookType: 'xlsx',
    bookSST: true,
    type: 'array',
  })
  try {
    FileSaver.saveAs(
      new Blob([wbout], {
        type: 'application/octet-stream',
      }),
      `用户反馈${new Date().valueOf()}.xlsx`
    )
  } catch (e) {
    console.log(e)
  }
}

const fileUploadSuccess = (response, uploadFile) => {
  if (response.code === 0) {
    formData.value.feedback_image.push(response.data.key)
  }
}

const fileRemove = (uploadFile) => {
  let imgKey
  if (uploadFile.response) {
    imgKey = uploadFile.response.data.key
  } else {
    imgKey = uploadFile.name
  }
  let index = formData.value.feedback_image.indexOf(imgKey)
  if (index > -1) {
    formData.value.feedback_image.splice(index, 1)
  }
}

const sortChange = ({ prop, order }) => {
  searchInfo.value.sort_prop = prop
  searchInfo.value.sort_desc = order !== 'ascending'
  getTableData()
}

const filePreview = (uploadFile) => {
  previewImgUrl.value = uploadFile.url
  imgDialogVisible.value = true
}

const onReset = () => {
  searchInfo.value = {}
  getTableData()
}

const onSubmit = () => {
  page.value = 1
  getTableData()
}

const pageSizeChange = (val) => {
  pageSize.value = val
  getTableData()
}

const pageChange = (val) => {
  page.value = val
  getTableData()
}

const execItem = (item) => {
  item.created_at_str = formatDate(item.created_at)
  return item
}

const getTableData = async () => {
  const table = await getUserFeedbackList({ page: page.value, page_size: pageSize.value, ...searchInfo.value })
  if (table.code === 0) {
    let resDataList = table.data.list || []
    let newList = []
    resDataList.forEach((item) => {
      item = execItem(item)
      newList.push(item)
    })
    tableData.value = newList
    total.value = table.data.total
  }
}

const deleteRow = (row) => {
  ElMessageBox.confirm('确定删除?').then(() => {
    deleteInfoFunc(row)
  })
}

const initShowDialogInfo = async (row) => {
  const res = await findUserFeedback({ id: row.id })
  if (res.code === 0) {
    formData.value = res.data || {}
    let img_list = res.data.feedback_image || []
    if (img_list.length > 0) {
      let fileListNew = []
      img_list.forEach((item) => {
        let img_full = getOssFileUrl(item)
        fileListNew.push({
          name: img_full,
          url: img_full,
        })
      })
      upFileList.value = fileListNew
    }
    dialogShow.value = true
  }
}

const viewFunc = async (row) => {
  dType.value = 'view'
  dTitle.value = '查看'
  showSubmitBtn.value = true
  if(row.reply_content){
    showSubmitBtn.value = false
  }
  initShowDialogInfo(row)
}

const updateFunc = async (row) => {
  dType.value = 'update'
  dTitle.value = '更新'
  initShowDialogInfo(row)
}

const deleteInfoFunc = async (row) => {
  const res = await deleteUserFeedback({ id: row.id })
  if (res.code === 0) {
    ElMessage({ type: 'success', message: '删除成功' })
    getTableData()
  }
}

const openDialog = () => {
  dType.value = 'create'
  dTitle.value = '创建'
  dialogShow.value = true
}

const closeDialog = () => {
  dialogShow.value = false
  upFileList.value = []
  formData.value = { user_id: 0, name: '', phone: '', region: 0, business: 0, feedback_content: '', star: 0 }
}

const enterDialog = async () => {
  elFormRef.value?.validate(async (valid) => {
    if (!valid) return
    let res
    switch (dType.value) {
      case 'create':
        res = await createUserFeedback(formData.value)
        break
      case 'update':
        res = await updateUserFeedback(formData.value)
        break
      default:
        res = await createUserFeedback(formData.value)
        break
    }
    if (res.code === 0) {
      ElMessage({ type: 'success', message: '创建/更改成功' })
      closeDialog()
      getTableData()
    }
  })
}

onBeforeMount(() => {
  getTableData()
})
</script>

<style scoped lang="scss">
.chat-main {
  overflow: auto;
  height: 600px;
  padding-left: 16px;
  padding-right: 16px;
  padding-top: 10px;
  display: flex;
  flex-direction: column;
}

.chat-ls {
  .chat-time {
    font-size: 12px;
    color: rgba(39, 40, 50, 0.3);
    line-height: 17px;
    padding: 5px 0px;
    text-align: center;
  }

  .msg-m {
    display: flex;
    padding: 10px 0;

    .user-img {
      flex: none;
      width: 40px;
      height: 40px;
      border-radius: 10px;
    }

    .message {
      flex: none;
      max-width: 480px;
    }

    .msg-text {
      font-size: 14px;
      color: rgba(39, 40, 50, 1);
      line-height: 22px;
      padding: 9px 12px;
    }

    .msg-img {
      max-width: 200px;
      border-radius: 10px;
      padding: 0 5px;
    }

    .msg-map {
      background: #fff;
      width: 464px;
      height: 284px;
      overflow: hidden;

      .map-name {
        font-size: 32px;
        color: rgba(39, 40, 50, 1);
        line-height: 44px;
        padding: 18px 24px 0 24px;
        //下面四行是单行文字的样式
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 1;
        overflow: hidden;
      }

      .map-address {
        font-size: 24px;
        color: rgba(39, 40, 50, 0.4);
        padding: 0 24px;
        //下面四行是单行文字的样式
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 1;
        overflow: hidden;
      }

      .map {
        padding-top: 8px;
        width: 464px;
        height: 190px;
      }
    }

    .voice {
      // width: 200px;
      min-width: 100px;
      max-width: 400px;
    }

    .voice-img {
      width: 28px;
      height: 36px;
    }
  }

  .msg-left {
    flex-direction: row;

    .msg-text {
      margin-left: 16px;
      background-color: #fff;
      border-radius: 0px 20px 20px 20px;
    }

    .ms-img {
      margin-left: 16px;
    }

    .msh-map {
      margin-left: 16px;
      border-radius: 0px 20px 20px 20px;
    }

    .voice {
      text-align: right;
    }

    .voice-img {
      float: left;
      transform: rotate(180deg);
      width: 28px;
      height: 36px;
      padding-bottom: 4px;
    }
  }

  .msg-right {
    flex-direction: row-reverse;

    .msg-text {
      margin-right: 16px;
      background-color: rgba(255, 228, 49, 0.8);
      border-radius: 20px 0px 20px 20px;
    }

    .ms-img {
      margin-right: 16px;
    }

    .msh-map {
      margin-left: 16px;
      border-radius: 20px 0px 20px 20px;
    }

    .voice {
      text-align: left;
    }

    .voice-img {
      float: right;
      padding: 4px;
      width: 28px;
      height: 36px;
    }
  }
}

.upload-demo {
  line-height: 32px;
}
</style>
