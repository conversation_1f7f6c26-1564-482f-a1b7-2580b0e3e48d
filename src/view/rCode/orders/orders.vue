<template>
  <div>
    <el-card shadow="hover">
      <el-form :inline="true" :model="searchInfo" @keyup.enter="onSubmit">
        <el-form-item label="创建时间范围">
          <el-date-picker v-model="searchInfo.date_range" :shortcuts="dateShortcuts" type="datetimerange" value-format="YYYY-MM-DD HH:mm:ss" />
        </el-form-item>
        <el-form-item label="区域" prop="retail_area_id">
          <el-select v-model="searchInfo.retail_area_id" clearable placeholder="请选择">
            <el-option v-for="item in userStore.areasList" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="订单类型" prop="cat_type">
          <el-select v-model="searchInfo.cat_type" clearable placeholder="请选择">
            <el-option v-for="item in userStore.catTypeList" :key="item" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="订单号" prop="order_no">
          <el-input v-model="searchInfo.order_no" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="用户ID" prop="user_id">
          <el-input v-model.number="searchInfo.user_id" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="订单状态" prop="order_state">
          <el-select v-model="searchInfo.order_state" clearable placeholder="请选择">
            <el-option v-for="item in userStore.orderStateOptions" :key="item" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>

        <el-form-item label="订单ID" prop="user_id">
          <el-input v-model.number="searchInfo.id" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="search" @click="onSubmit">查询</el-button>
          <el-button icon="refresh" @click="onReset">重置</el-button>
          <el-button type="success" icon="document" @click="onExport" :disabled="total === 0">导出</el-button>

          <el-popover placement="left" :width="400" trigger="click" :visible="visible">
            <template #reference>
              <el-button
                type="primary"
                icon="Setting"
                @click="visible = true"
              >配置显示字段</el-button>
            </template>

            <!-- 配置列面板 -->
            <transition name="fade">
              <div>
                <div>选择显示字段</div>
                <div>
                  <el-row>
                    <!-- <el-col :span="8"> <el-checkbox v-model="columnsList.id" disabled>ID</el-checkbox> </el-col> -->
                    <el-col :span="8"> <el-checkbox v-model="columnsList.created_at">创建时间</el-checkbox></el-col>
                    <el-col :span="8"> <el-checkbox v-model="columnsList.cat_type">订单类型</el-checkbox> </el-col>
                    <el-col :span="8"> <el-checkbox v-model="columnsList.user_id">用户ID</el-checkbox></el-col>
                    <el-col :span="8"> <el-checkbox v-model="columnsList.mobile">用户手机号</el-checkbox></el-col>
                    <el-col :span="8"> <el-checkbox v-model="columnsList.nickname">用户昵称</el-checkbox></el-col>
                    <el-col :span="8"> <el-checkbox v-model="columnsList.retail_area_id">区域</el-checkbox></el-col>
                    <el-col :span="8"> <el-checkbox v-model="columnsList.order_no">订单号</el-checkbox></el-col>
                    <el-col :span="8"> <el-checkbox v-model="columnsList.order_state">订单状态</el-checkbox></el-col>
                    <el-col :span="8"> <el-checkbox v-model="columnsList.pick_code">取货码</el-checkbox></el-col>
                    <el-col :span="8"> <el-checkbox v-model="columnsList.obtain_time">取货时间</el-checkbox></el-col>
                    <el-col :span="8"> <el-checkbox v-model="columnsList.price">价格</el-checkbox></el-col>
                    <el-col :span="8"> <el-checkbox v-model="columnsList.sub_order_num">子订单数量</el-checkbox></el-col>
                  </el-row>
                </div>
              </div>
            </transition>
            <div style="text-align: right; margin: 20px 0 0">
              <el-button type="text" @click="visible = false">取消</el-button>
              <el-button type="primary" plain @click="saveColumn">保存</el-button>
            </div>
            <!-- <template #reference>
              <i
                class="el-icon-setting"
                style="font-size: 22px; cursor: pointer"
                @click="visible = true"
              ></i>
            </template> -->
          </el-popover>
        </el-form-item>
      </el-form>
      <el-table ref="tableRef" :data="tableData" :max-height="680" border row-key="id" show-overflow-tooltip @sort-change="sortChange">
        <el-table-column :width="100" align="center" fixed="left" label="ID" prop="id" />
        <el-table-column :width="160" align="center" label="创建时间" prop="created_at" sortable="custom" v-if="columnsList.created_at">
          <template #default="scope">{{ formatDate(scope.row.created_at_str) }}</template>
        </el-table-column>
        <el-table-column :width="160" align="center" label="订单类型" prop="cat_type" sortable="custom"  v-if="columnsList.cat_type">
          <template #default="scope">{{ userStore.GetCatTypeName(scope.row.cat_type) }}</template>
        </el-table-column>
        <el-table-column :width="120" align="center" label="用户ID" prop="user_id" sortable="custom" v-if="columnsList.user_id"/>
        <el-table-column :width="120" align="center" label="用户手机号" prop="mobile" v-if="columnsList.mobile" />
        <el-table-column :width="120" align="center" label="用户昵称" prop="nickname" v-if="columnsList.nickname" />
        <el-table-column :min-width="160" align="center" label="区域" prop="retail_area_id" sortable="custom"  v-if="columnsList.retail_area_id">
          <template #default="scope">{{ userStore.GetAreaName(scope.row.retail_area_id) }}</template>
        </el-table-column>
        <el-table-column :width="240" align="center" label="订单号" prop="order_no" v-if="columnsList.order_no" />
        <el-table-column :width="120" align="center" label="订单状态" v-if="columnsList.order_state">
          <template #default="scope">
            <el-tag type="success" v-if="scope.row.order_state === 200">
              {{ userStore.GetOrderState(scope.row.order_state) }}
            </el-tag>
            <el-tag type="warning" v-else-if="scope.row.order_state === 2">
              {{ userStore.GetOrderState(scope.row.order_state) }}
            </el-tag>
            <el-tag type="danger" v-else>
              {{ userStore.GetOrderState(scope.row.order_state) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column :width="130" align="center" label="取货码" prop="pick_code" v-if="columnsList.pick_code" />
        <el-table-column :width="130" align="center" label="取货时间" prop="obtain_time" sortable="custom"  show-overflow-tooltip v-if="columnsList.obtain_time" />
        <el-table-column :width="120" align="center" label="价格" prop="price" sortable="custom" v-if="columnsList.price" />
        <el-table-column :width="160" align="center" label="子订单数量" prop="sub_order_num" v-if="columnsList.sub_order_num">
          <template #default="scope">
            <el-button link type="primary" @click="viewSubOrdersFunc(scope.row)">查看({{ scope.row.sub_order_num }})</el-button>
          </template>
        </el-table-column>
        <el-table-column :width="160" align="center" fixed="right" label="操作">
          <template #default="scope">
            <el-button link type="primary" @click="updateFunc(scope.row, 2)" v-if="scope.row.order_state === 200"> 待取餐 </el-button>
            <el-button link type="primary" @click="updateFunc(scope.row, 200)" v-else-if="scope.row.order_state === 2"> 已取餐 </el-button>
            <el-button link type="primary" disabled v-else> 已取消 </el-button>
            <el-button link type="primary" @click="viewFunc(scope.row)">查看详情</el-button>
            <!--<el-button type="warning" @click="updateFunc(scope.row)">变更</el-button>-->
            <!--<el-button type="danger" @click="deleteRow(scope.row)">删除</el-button>-->
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="page"
        :layout="PageLayout"
        :page-size="pageSize"
        :page-sizes="PageSizesLarge"
        :total="total"
        @current-change="pageChange"
        @size-change="pageSizeChange"
      />
    </el-card>
    <el-dialog v-model="dialogShow" :before-close="closeDialog" :title="dTitle" destroy-on-close top="10px">
      <el-form ref="elFormRef" :disabled="dType === 'view'" :model="formData" :rules="formDataRule" label-position="right" label-width="80px">
        <el-form-item label="用户ID" prop="user_id">
          <el-input-number v-model="formData.user_id" style="width: 100%" />
        </el-form-item>
        <el-form-item label="区域ID" prop="retail_area_id">
          <el-select v-model="formData.retail_area_id" style="width: 100%">
            <el-option v-for="item in userStore.areasList" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="订单号" prop="order_no">
          <el-input v-model="formData.order_no" :maxlength="32" clearable show-word-limit />
        </el-form-item>
        <el-form-item label="订单状态" prop="order_state">
          <el-select v-model="formData.order_state" style="width: 100%">
            <el-option v-for="item in userStore.orderStateOptions" :key="item" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="取货码" prop="pick_code">
          <el-input v-model="formData.pick_code" :maxlength="16" clearable show-word-limit />
        </el-form-item>
        <el-form-item label="价格" prop="price">
          <el-input-number v-model="formData.price" :precision="2" style="width: 100%" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div v-show="dType !== 'view'" class="dialog-footer">
          <el-button @click="closeDialog">取 消</el-button>
          <el-button type="primary" @click="enterDialog">确 定</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog v-model="subOrderDialogShow" :before-close="closeSubDialog" title="订单详情" destroy-on-close width="50%" top="10px">
      <el-table ref="tableRef" :data="subOrderDialogTableData" :max-height="680" border row-key="id" show-overflow-tooltip>
        <el-table-column :width="100" align="center" fixed="left" label="子订单ID" prop="id" />
        <el-table-column :min-width="120" align="center" label="商品编码" prop="products_info.code" />

        <el-table-column :min-width="160" align="center" label="名称" prop="amount">
          <template #default="scope">{{ scope.row.products_info.name }}</template>
        </el-table-column>
        <el-table-column :width="70" align="center" label="图片">
          <template #default="scope">
            <el-image :preview-src-list="getProPreviewList(scope.row)" :src="getProPreviewSrc(scope.row)" fit="contain" style="width: 30px; height: 30px"></el-image>
          </template>
        </el-table-column>
        <el-table-column :width="80" align="center" label="单价">
          <template #default="scope">{{ scope.row.products_info.price }}</template>
        </el-table-column>
        <el-table-column :width="80" align="center" label="数量" prop="amount" />
        <el-table-column :width="90" align="center" label="总价" prop="price" />
      </el-table>
    </el-dialog>
  </div>
</template>

<script setup>
import * as XLSX from 'xlsx'
import { dayjs, ElMessage, ElMessageBox } from 'element-plus'
import { createOrders, deleteOrders, findOrders, getOrdersList, updateOrders } from '@/api/retail/orders'
import { formatDate, getOssFileUrl, getOssFileUrlList } from '@/utils/format'
import { onBeforeMount, reactive, ref } from 'vue'
import { PageLayout, PageSizesLarge } from '@/utils/const'
import { useRoute, useRouter } from 'vue-router'
import { useUserStore } from '@/pinia/modules/user'
import { dateShortcuts } from '@/utils/date_shortcuts'

const columnsList = ref({
  // id: true,
  created_at: true,
  cat_type: true,
  user_id: true,
  mobile: true,
  nickname: true,
  retail_area_id: true,
  order_no: true,
  order_state: true,
  pick_code: true,
  obtain_time: true,
  price: true,
  sub_order_num: true,
})
const visible = ref(false)

const saveColumn = () => {
  localStorage.setItem('order_columnSet', JSON.stringify(columnsList.value))
  visible.value = false
}
const userStore = useUserStore()
const router = useRouter()
const route = useRoute()
const subOrderDialogShow = ref(false)
const subOrderDialogTableData = ref([])
const dialogShow = ref(false)
const dTitle = ref('')
const dType = ref('')
const elFormRef = ref()
const page = ref(1)
const total = ref(0)
const pageSize = ref(100)
const tableData = ref([])
const searchInfo = ref({
  date_range: [],
  cat_type: 1,
})
const tableRef = ref(null)
const formData = ref({
  user_id: 0,
  retail_area_id: 0,
  order_no: '',
  order_state: 0,
  pick_code: '',
  price: 0,
  sub_order_num: 0,
  pay_method_id: 0,
})
const formDataRule = reactive({
  userId: [{ required: true, message: '请完善该值', trigger: ['input', 'blur'] }],
  retailAreaId: [{ required: true, message: '请完善该值', trigger: ['input', 'blur'] }],
  orderNo: [
    { required: true, message: '请完善该值', trigger: ['input', 'blur'] },
    {
      whitespace: true,
      message: '不能只输入空格',
      trigger: ['input', 'blur'],
    },
  ],
  orderState: [{ required: true, message: '请完善该值', trigger: ['input', 'blur'] }],
  payMethodId: [{ required: true, message: '请完善该值', trigger: ['input', 'blur'] }],
})

const sortChange = ({ prop, order }) => {
  searchInfo.value.sort_prop = prop
  searchInfo.value.sort_desc = order !== 'ascending'
  getTableData()
}

const onReset = () => {
  searchInfo.value = {}
  getTableData()
}

const onExport = () => {
  const titleRow = ['创建时间', '订单类型', '用户手机号', '用户昵称', '区域', '订单号', '订单状态', '取货码', '取货时间', '总价格', '商品名称', '商品数量', '商品单价', '商品总价','商品编码']
  let excelJsonList = []
  tableData.value.forEach((item) => {
    let productList = []
    let subOrderList = item.sub_order_list || []
    subOrderList.forEach((subOrder) => {
      let subOrderProductsInfo = subOrder.products_info || {}
      let imgUrlList = subOrderProductsInfo.image_url_list || []
      let imgUrlFull = ''
      if (imgUrlList.length > 0) {
        imgUrlFull = getOssFileUrl(imgUrlList[0])
      }
      productList.push({
        product_name: subOrderProductsInfo.name,
        product_img_url_full: imgUrlFull,
        product_num: subOrder.amount,
        product_price: subOrderProductsInfo.price,
        product_total_price: subOrder.price,
        product_code: subOrderProductsInfo.code,
      })
    })
    excelJsonList.push({
      created_at_str: item.created_at_str,
      cat_type_str: userStore.GetCatTypeName(item.cat_type),
      mobile: item.mobile,
      nickname: item.nickname,
      retail_area_str: userStore.GetAreaName(item.retail_area_id),
      order_no: item.order_no,
      order_state_str: userStore.GetOrderState(item.order_state),
      pick_code: item.pick_code,
      obtain_time: item.obtain_time,
      price: item.price,
      productList: productList,
    })
  })
  jsonToSheet3(excelJsonList, titleRow)
}

const jsonToSheet3 = (jsonData, titleRow) => {
  // Create a new workbook and a new worksheet
  const wb = XLSX.utils.book_new()
  const ws = XLSX.utils.aoa_to_sheet([titleRow])
  // Define the widths for each column
  ws['!cols'] = [
    { wch: 20 }, // '创建时间' column
    { wch: 10 }, // '订单类型' column
    { wch: 15 }, // '用户手机号' column
    { wch: 10 }, // '用户昵称' column
    { wch: 10 }, // '区域' column
    { wch: 30 }, // '订单号' column
    { wch: 10 }, // '订单状态' column
    { wch: 10 }, // '取货码' column
    { wch: 20 }, // '取货时间' column
    { wch: 10 }, // '总价格' column
    { wch: 40 }, // '商品名称' column
    { wch: 10 }, // '商品数量' column
    { wch: 10 }, // '商品单价' column
    { wch: 10 }, // '商品总价' column
    { wch: 10 }, // '商品编码' column
  ]
  let currentRow = 1 // Keep track of the current row in the sheet

  jsonData.forEach((order) => {
    const orderProductCount = order.productList.length
    const orderInfo = [
      order.created_at_str,
      order.cat_type_str,
      order.mobile,
      order.nickname,
      order.retail_area_str,
      order.order_no,
      order.order_state_str,
      order.pick_code,
      order.obtain_time, //取货时间
      Number(order.price),
    ]

    // Start row for the current order
    const startRow = currentRow + 1

    order.productList.forEach((product) => {
      // Push a row for each product
      const productRow = [...orderInfo, product.product_name, product.product_num, Number(product.product_price), Number(product.product_total_price),product.product_code]
      XLSX.utils.sheet_add_aoa(ws, [productRow], { origin: -1 })
      currentRow++
    })

    // End row for the current order
    const endRow = currentRow

    // Merge cells for the order information if more than 1 product
    if (orderProductCount > 1) {
      for (let i = 0; i < orderInfo.length; i++) {
        ws['!merges'] = ws['!merges'] || []
        ws['!merges'].push(XLSX.utils.decode_range(`${XLSX.utils.encode_col(i)}${startRow}:${XLSX.utils.encode_col(i)}${endRow}`))
      }
    }
  })

  // Add the worksheet to the workbook
  XLSX.utils.book_append_sheet(wb, ws, 'Orders')
  let nowStr = dayjs().format('YYYY_MM_DD_HH_mm_ss')
  // Write the workbook to a file
  XLSX.writeFile(wb, `订单导出${nowStr}.xlsx`)
}

const onSubmit = () => {
  page.value = 1
  getTableData()
}

const pageSizeChange = (val) => {
  pageSize.value = val
  getTableData()
}

const pageChange = (val) => {
  page.value = val
  getTableData()
}

const execItem = (item) => {
  item.created_at_str = formatDate(item.created_at)
  return item
}

const getTableData = async () => {
  const table = await getOrdersList({ page: page.value, pageSize: pageSize.value, ...searchInfo.value })
  if (table.code === 0) {
    let resDataList = table.data.list || []
    let newList = []
    resDataList.forEach((item) => {
      item = execItem(item)
      newList.push(item)
    })
    tableData.value = newList
    total.value = table.data.total
  }
}

const deleteRow = (row) => {
  ElMessageBox.confirm('确定删除?').then(() => {
    deleteInfoFunc(row)
  })
}

const initShowDialogInfo = async (row) => {
  const res = await findOrders({ id: row.id })
  if (res.code === 0) {
    formData.value = res.data || {}
    formData.value.price = parseFloat(formData.value.price)
    dialogShow.value = true
  }
}

const viewFunc = async (row) => {
  dType.value = 'view'
  dTitle.value = '查看'
  initShowDialogInfo(row)
}

const getProPreviewList = (row) => {
  if (row.products_info.image_url_list && row.products_info.image_url_list.length > 0) {
    return getOssFileUrlList(row.products_info.image_url_list)
  } else {
    return []
  }
}
const getProPreviewSrc = (row) => {
  if (row.products_info.image_url_list && row.products_info.image_url_list.length > 0) {
    return getOssFileUrl(row.products_info.image_url_list[0])
  } else {
    return ''
  }
}

const viewSubOrdersFunc = async (row) => {
  if (row.sub_order_list.length > 0) {
    subOrderDialogTableData.value = row.sub_order_list
    subOrderDialogShow.value = true
  }
}

const closeSubDialog = () => {
  subOrderDialogShow.value = false
  subOrderDialogTableData.value = []
}

const updateFunc = async (row, state) => {
  dType.value = 'update'
  const res = await findOrders({ id: row.id })
  if (res.code === 0) {
    formData.value = res.data || {}
    formData.value.order_state = state
    const val = await updateOrders(formData.value)
    if (val.code === 0) {
      ElMessage.success('状态已修改')
      getTableData()
    }
  }
}

const deleteInfoFunc = async (row) => {
  const res = await deleteOrders({ id: row.id })
  if (res.code === 0) {
    ElMessage({ type: 'success', message: '删除成功' })
    getTableData()
  }
}

const openDialog = () => {
  dType.value = 'create'
  dTitle.value = '创建'
  dialogShow.value = true
}

const closeDialog = () => {
  dialogShow.value = false
  formData.value = {
    user_id: 0,
    retail_area_id: 0,
    order_no: '',
    order_state: 0,
    pick_code: '',
    price: 0,
    sub_order_num: 0,
    pay_method_id: 0,
  }
}

const enterDialog = async () => {
  elFormRef.value?.validate(async (valid) => {
    if (!valid) return
    let res
    switch (dType.value) {
      case 'create':
        res = await createOrders(formData.value)
        break
      case 'update':
        res = await updateOrders(formData.value)
        break
      default:
        res = await createOrders(formData.value)
        break
    }
    if (res.code === 0) {
      ElMessage({ type: 'success', message: '创建/更改成功' })
      closeDialog()
      getTableData()
    }
  })
}

onBeforeMount(() => {
  // 接收页面传参
  const query = route.query
  console.log(query)
  if (query.id) {
    searchInfo.value.id = query.id || ''
    searchInfo.value.cat_type = ''
  }

  if (localStorage.getItem('order_columnSet')) {
    columnsList.value = JSON.parse(localStorage.getItem('order_columnSet'))
  }
  getTableData()
})
</script>

<style></style>
