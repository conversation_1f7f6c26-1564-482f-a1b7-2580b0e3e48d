<template>
  <div>
    <el-card shadow="hover">
      <el-form :inline="true" :model="searchInfo" @keyup.enter="onSubmit">
        <el-form-item label="区域名称" prop="name">
          <el-input v-model="searchInfo.name" clearable placeholder="请输入"/>
        </el-form-item>
        <el-form-item label="联系手机号" prop="mobile">
          <el-input v-model="searchInfo.mobile" clearable placeholder="请输入"/>
        </el-form-item>
        <el-form-item label="地址" prop="address">
          <el-input v-model="searchInfo.address" clearable placeholder="请输入"/>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="search" @click="onSubmit">查询</el-button>
          <el-button icon="refresh" @click="onReset">重置</el-button>
          <el-button icon="plus" type="success" @click="openDialog">新增</el-button>
        </el-form-item>
      </el-form>
      <el-table ref="tableRef" :max-height="680" :data="tableData" border row-key="id" show-overflow-tooltip
                @sort-change="sortChange">
        <el-table-column :width="100" align="center" fixed="left" label="ID" prop="id"/>
        <el-table-column :width="160" align="center" label="创建时间" prop="created_at_str"/>
        <el-table-column :min-width="120" align="center" label="区域名称" prop="name"/>
        <el-table-column :width="160" align="center" label="外卖联系手机号" prop="mobile"/>
        <el-table-column :width="160" align="center" label="团购联系手机号" prop="group_buy_mobile"/>
        <el-table-column :width="120" align="center" label="外卖状态" prop="takeaway_state" sortable>
          <template #default="scope">
            <el-switch v-model="scope.row.takeaway_state" :active-value="1" :inactive-value="2"
                       @change="updateAreaSwitch(scope.row)"></el-switch>
          </template>
        </el-table-column>
        <el-table-column :width="120" align="center" label="团购状态" prop="group_buy_state" sortable>
          <template #default="scope">
            <el-switch v-model="scope.row.group_buy_state" :active-value="1" :inactive-value="2"
                       @change="updateAreaSwitch(scope.row)"></el-switch>
          </template>
        </el-table-column>
        <el-table-column :width="280" align="center" fixed="right" label="操作">
          <template #default="scope">
            <el-button type="primary" @click="viewFunc(scope.row)">查看详情</el-button>
            <el-button type="warning" @click="updateFunc(scope.row)">变更</el-button>
            <el-button type="danger" @click="deleteRow(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="page"
        :layout="PageLayout"
        :page-size="pageSize"
        :page-sizes="PageSizes"
        :total="total"
        @current-change="pageChange"
        @size-change="pageSizeChange"
      />
    </el-card>
    <el-dialog v-model="dialogShow" :before-close="closeDialog" :title="dTitle" destroy-on-close top="10px">
      <el-form ref="elFormRef" :disabled="dType === 'view'" :model="formData" :rules="formDataRule"
               label-position="right" label-width="80px">
        <el-form-item label="区域名称" prop="name">
          <el-input v-model="formData.name" :maxlength="16" clearable show-word-limit/>
        </el-form-item>
        <el-form-item label="外卖手机号" prop="mobile">
          <el-input v-model="formData.mobile" :maxlength="16" clearable show-word-limit/>
        </el-form-item>
        <el-form-item label="外卖地址" prop="address">
          <el-input v-model="formData.address" :maxlength="256" clearable show-word-limit/>
        </el-form-item>
        <el-form-item label="团购手机号" prop="group_buy_mobile">
          <el-input v-model="formData.group_buy_mobile" :maxlength="16" clearable show-word-limit/>
        </el-form-item>
        <el-form-item label="团购地址" prop="group_buy_address">
          <el-input v-model="formData.group_buy_address" :maxlength="256" clearable show-word-limit/>
        </el-form-item>
        <el-form-item label="经度" prop="longitude">
          <el-input v-model="formData.longitude" :maxlength="32" clearable show-word-limit/>
        </el-form-item>
        <el-form-item label="纬度" prop="latitude">
          <el-input v-model="formData.latitude" :maxlength="32" clearable show-word-limit/>
        </el-form-item>
        <el-form-item label="外卖状态" prop="takeaway_state">
          <!--<el-input-number v-model="formData.takeaway_state" style="width:100%"/>-->
          <el-switch v-model="formData.takeaway_state" :active-value="1" :inactive-value="2"></el-switch>
        </el-form-item>
        <el-form-item label="团购状态" prop="group_buy_state">
          <!--<el-input-number v-model="formData.group_buy_state" style="width:100%"/>-->
          <el-switch v-model="formData.group_buy_state" :active-value="1" :inactive-value="2"></el-switch>
        </el-form-item>
      </el-form>
      <template #footer>
        <div v-show="dType !== 'view'" class="dialog-footer">
          <el-button @click="closeDialog">取 消</el-button>
          <el-button type="primary" @click="enterDialog">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import {
  createRetailAreas,
  deleteRetailAreas,
  findRetailAreas,
  getRetailAreasList,
  updateRetailAreas
} from '@/api/retail/retailAreas'
import {formatDate} from '@/utils/format'
import {ElMessage, ElMessageBox} from 'element-plus'
import {onBeforeMount, reactive, ref} from 'vue'
import {PageLayout, PageSizes} from "@/utils/const"
import {useRoute, useRouter} from 'vue-router'


const router = useRouter()
const route = useRoute()
const dialogShow = ref(false)
const dTitle = ref('')
const dType = ref('')
const elFormRef = ref()
const page = ref(1)
const total = ref(0)
const pageSize = ref(100)
const tableData = ref([])
const searchInfo = ref({})
const formData = ref({
  name: '',
  mobile: '',
  address: '',
  longitude: '',
  latitude: '',
  takeaway_state: 1,
  group_buy_state: 1,
})
const formDataRule = reactive({
  name: [{
    required: true,
    message: '请完善该值',
    trigger: ['input', 'blur']
  }, {whitespace: true, message: '不能只输入空格', trigger: ['input', 'blur']}],
  address: [{required: true, message: '请完善该值', trigger: ['input', 'blur']}, {
    whitespace: true,
    message: '不能只输入空格',
    trigger: ['input', 'blur']
  }],
})

const sortChange = ({prop, order}) => {
  searchInfo.value.sort_prop = prop
  searchInfo.value.sort_desc = order !== 'ascending'
  getTableData()
}

const onReset = () => {
  searchInfo.value = {}
  getTableData()
}

const onSubmit = () => {
  page.value = 1
  getTableData()
}

const pageSizeChange = (val) => {
  pageSize.value = val
  getTableData()
}

const pageChange = (val) => {
  page.value = val
  getTableData()
}

const execItem = (item) => {
  item.created_at_str = formatDate(item.created_at)
  return item
}

const getTableData = async () => {
  const table = await getRetailAreasList({page: page.value, pageSize: pageSize.value, ...searchInfo.value})
  if (table.code === 0) {
    let resDataList = table.data.list || []
    let newList = []
    resDataList.forEach(item => {
      item = execItem(item)
      newList.push(item)
    })
    tableData.value = newList
    total.value = table.data.total
  }
}

const deleteRow = (row) => {
  ElMessageBox.confirm('确定删除?').then(() => {
    deleteInfoFunc(row)
  })
}

const initShowDialogInfo = async (row) => {
  const res = await findRetailAreas({id: row.id})
  if (res.code === 0) {
    formData.value = res.data || {}
    dialogShow.value = true
  }
}

const viewFunc = async (row) => {
  dType.value = 'view'
  dTitle.value = '查看'
  initShowDialogInfo(row)
}

const updateFunc = async (row) => {
  dType.value = 'update'
  dTitle.value = '更新'
  initShowDialogInfo(row)
}

const updateAreaSwitch = async (row) => {
  let res = await updateRetailAreas(row)
  if (res.code === 0) {
    ElMessage({type: 'success', message: '成功'})
    getTableData()
  }
}

const deleteInfoFunc = async (row) => {
  const res = await deleteRetailAreas({id: row.id})
  if (res.code === 0) {
    ElMessage({type: 'success', message: '删除成功'})
    getTableData()
  }
}

const openDialog = () => {
  dType.value = 'create'
  dTitle.value = '创建'
  dialogShow.value = true
}

const closeDialog = () => {
  dialogShow.value = false
  formData.value = {
    name: '',
    mobile: '',
    address: '',
    longitude: '',
    latitude: '',
    takeaway_state: 1,
    group_buy_state: 1,
  }
}

const enterDialog = async () => {
  elFormRef.value?.validate(async (valid) => {
    if (!valid) return
    let res
    switch (dType.value) {
      case 'create':
        res = await createRetailAreas(formData.value)
        break
      case 'update':
        res = await updateRetailAreas(formData.value)
        break
      default:
        res = await createRetailAreas(formData.value)
        break
    }
    if (res.code === 0) {
      ElMessage({type: 'success', message: '创建/更改成功'})
      closeDialog()
      getTableData()
    }
  })
}

onBeforeMount(() => {
  getTableData()
})
</script>

<style></style>
