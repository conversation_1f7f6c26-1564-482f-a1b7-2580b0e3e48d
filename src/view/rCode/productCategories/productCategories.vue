
<template>
  <div>
    <el-card shadow="hover">
      <el-form :inline="true" :model="searchInfo" @keyup.enter="onSubmit">
        <el-form-item label="区域" prop="retail_area_id">
          <el-select v-model="searchInfo.retail_area_id" clearable placeholder="请选择">
            <el-option v-for="item in userStore.areasList" :key="item.id" :label="item.name" :value="item.id"/>
          </el-select>
        </el-form-item>
        <el-form-item label="分类类型" prop="cat_type">
          <el-select v-model="searchInfo.cat_type" clearable placeholder="请选择">
            <el-option v-for="item in userStore.catTypeList" :key="item.value" :label="item.label" :value="item.value"/>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="search" @click="onSubmit">查询</el-button>
          <el-button icon="refresh" @click="onReset">重置</el-button>
          <el-button icon="plus" type="success" @click="openDialog">新增</el-button>
        </el-form-item>
      </el-form>
      <el-table ref="tableRef" :max-height="680" :data="tableData" border row-key="id" show-overflow-tooltip
                @sort-change="sortChange">
        <el-table-column :width="100" align="center" fixed="left" label="ID" prop="id"/>
        <el-table-column :width="160" align="center" label="创建时间" prop="created_at_str"/>
        <el-table-column :width="190" align="center" label="区域" prop="retail_area_id_str"/>
        <el-table-column :min-width="120" align="center" label="名称" prop="name"/>
        <el-table-column :width="140" align="center" label="类型" prop="cat_type_str"/>
        <el-table-column :width="140" align="center" label="排序" prop="sort" sortable="custom"/>
        <el-table-column :width="280" align="center" fixed="right" label="操作">
          <template #default="scope">
            <el-button type="primary" @click="viewFunc(scope.row)">查看详情</el-button>
            <el-button type="warning" @click="updateFunc(scope.row)">变更</el-button>
            <el-button type="danger" @click="deleteRow(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="page"
        :layout="PageLayout"
        :page-size="pageSize"
        :page-sizes="PageSizes"
        :total="total"
        @current-change="pageChange"
        @size-change="pageSizeChange"
      />
    </el-card>
    <el-dialog v-model="dialogShow" :before-close="closeDialog" :title="dTitle" destroy-on-close top="10px">
      <el-form ref="elFormRef" :disabled="dType === 'view'" :model="formData" :rules="formDataRule"
               label-position="right" label-width="80px">
        <el-form-item label="区域" prop="retail_area_id">
          <el-select v-model="formData.retail_area_id" style="width:100%">
            <el-option v-for="item in userStore.areasList" :key="item.id" :label="item.name" :value="item.id"/>
          </el-select>
        </el-form-item>
        <el-form-item label="名称" prop="name">
          <el-input v-model="formData.name" :maxlength="32" clearable show-word-limit/>
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input v-model.number="formData.sort"/>
        </el-form-item>
        <el-form-item label="类型" prop="cat_type">
          <el-select v-model="formData.cat_type" style="width:100%">
            <el-option v-for="item in userStore.catTypeList" :key="item.value" :label="item.label" :value="item.value"/>
          </el-select>
        </el-form-item>
        <el-form-item v-if="formData.cat_type === 3" label="日期区间" prop="obtain_time_range" :rules="{ required: true, message: '请选择日期区间', trigger: 'blur' }">
          <el-date-picker
        v-model="formData.obtain_time_range"
        type="daterange"
        range-separator="To"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        value-format="YYYY-MM-DD"
      />
        </el-form-item>
      </el-form>
      <template #footer>
        <div v-show="dType !== 'view'" class="dialog-footer">
          <el-button @click="closeDialog">取 消</el-button>
          <el-button type="primary" @click="enterDialog">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import {
  createProductCategories,
  deleteProductCategories,
  findProductCategories,
  getProductCategoriesList,
  updateProductCategories
} from '@/api/retail/productCategories'
import {formatDate} from '@/utils/format'
import {ElMessage, ElMessageBox} from 'element-plus'
import {onBeforeMount, reactive, ref} from 'vue'
import {PageLayout, PageSizes} from "@/utils/const"
import {useRoute, useRouter} from 'vue-router'
import {useUserStore} from "@/pinia/modules/user";

const userStore = useUserStore()
const router = useRouter()
const route = useRoute()
const dialogShow = ref(false)
const dTitle = ref('')
const dType = ref('')
const elFormRef = ref()
const page = ref(1)
const total = ref(0)
const pageSize = ref(100)
const tableData = ref([])
const searchInfo = ref({})
const formData = ref({retail_area_id: 0, name: '', cat_type: 0,})
const formDataRule = reactive({
  retailAreaId: [{required: true, message: '请完善该值', trigger: ['input', 'blur']},],
  name: [{required: true, message: '请完善该值', trigger: ['input', 'blur']}, {
    whitespace: true,
    message: '不能只输入空格',
    trigger: ['input', 'blur']
  }],
  catType: [{required: true, message: '请完善该值', trigger: ['input', 'blur']},],
})

const sortChange = ({ prop, order }) => {
  searchInfo.value.sort_prop = prop
  searchInfo.value.sort_desc = order !== 'ascending'
  getTableData()
}

const onReset = () => {
  searchInfo.value = {}
  getTableData()
}

const onSubmit = () => {
  page.value = 1
  getTableData()
}

const pageSizeChange = (val) => {
  pageSize.value = val
  getTableData()
}

const pageChange = (val) => {
  page.value = val
  getTableData()
}

const execItem = (item) => {
  item.created_at_str = formatDate(item.created_at)
  item.retail_area_id_str = userStore.GetAreaName(item.retail_area_id)
  item.cat_type_str = userStore.GetCatTypeName(item.cat_type)

  return item
}

const getTableData = async() => {
  const table = await getProductCategoriesList({page: page.value, pageSize: pageSize.value, ...searchInfo.value})
  if (table.code === 0) {
    let resDataList = table.data.list || []
    let newList = []
    resDataList.forEach(item => {
      item = execItem(item)
      newList.push(item)
    })
    tableData.value = newList
    total.value = table.data.total
  }
}

const deleteRow = (row) => {
  ElMessageBox.confirm('确定删除?').then(() => {
    deleteInfoFunc(row)
  })
}

const initShowDialogInfo = async (row) => {
  const res = await findProductCategories({id: row.id})
  if (res.code === 0) {
    formData.value = res.data || {}
    dialogShow.value = true
  }
}

const viewFunc = async (row) => {
  dType.value = 'view'
  dTitle.value = '查看'
  initShowDialogInfo(row)
}

const updateFunc = async (row) => {
  dType.value = 'update'
  dTitle.value = '更新'
  initShowDialogInfo(row)
}

const deleteInfoFunc = async (row) => {
  const res = await deleteProductCategories({id: row.id})
  if (res.code === 0) {
    ElMessage({type: 'success', message: '删除成功'})
    getTableData()
  }
}

const openDialog = () => {
  dType.value = 'create'
  dTitle.value = '创建'
  dialogShow.value = true
}

const closeDialog = () => {
  dialogShow.value = false
  formData.value = {retail_area_id: 0, name: '', cat_type: 0,}
}

const enterDialog = async () => {
  elFormRef.value?.validate(async (valid) => {
    if (!valid) return
    let res
    switch (dType.value) {
      case 'create':
        res = await createProductCategories(formData.value)
        break
      case 'update':
        res = await updateProductCategories(formData.value)
        break
      default:
        res = await createProductCategories(formData.value)
        break
    }
    if (res.code === 0) {
      ElMessage({type: 'success', message: '创建/更改成功'})
      closeDialog()
      getTableData()
    }
  })
}

onBeforeMount(() => {
  getTableData()
})
</script>

<style></style>
