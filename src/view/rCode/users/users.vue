<template>
  <div>
    <el-card shadow="hover">
      <el-form :inline="true" :model="searchInfo" @keyup.enter="onSubmit">
        <el-form-item label="区域" prop="retail_area_id">
          <el-select v-model="searchInfo.retail_area_id" clearable placeholder="请选择">
            <el-option v-for="item in userStore.areasList" :key="item.id" :label="item.name" :value="item.id"/>
          </el-select>
        </el-form-item>
        <el-form-item label="手机号" prop="mobile">
          <el-input v-model="searchInfo.mobile" clearable placeholder="请输入"/>
        </el-form-item>
        <!--<el-form-item label="昵称" prop="nickname"><el-input v-model="searchInfo.nickname" clearable placeholder="请输入"/></el-form-item>-->
        <el-form-item>
          <el-button type="primary" icon="search" @click="onSubmit">查询</el-button>
          <el-button icon="refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
      <el-table ref="tableRef" :max-height="690" :data="tableData" border row-key="id" show-overflow-tooltip
                @sort-change="sortChange">
        <el-table-column :width="100" align="center" fixed="left" label="ID" prop="id"/>
        <el-table-column :width="160" align="center" label="区域" prop="retail_area_str"/>
        <el-table-column :width="160" align="center" label="注册时间" prop="created_at_str"/>
        <el-table-column :width="70" align="center" label="头像" prop="avatar">
          <template #default="scope">
            <el-image
              v-if="scope.row.avatar"
              :preview-src-list="[scope.row.avatar]"
              :src="scope.row.avatar"
              fit="cover"
              style="width: 30px; height: 30px"
            />
          </template>
        </el-table-column>
        <el-table-column :width="120" align="center" label="手机号" prop="mobile"/>
        <el-table-column :min-width="120" align="center" label="昵称" prop="nickname"/>
        <el-table-column :width="160" align="center" label="登录时间" prop="login_at_str" sortable/>
        <el-table-column :width="210" align="center" fixed="right" label="操作">
          <template #default="scope">
            <el-button type="primary" @click="viewFunc(scope.row)">查看详情</el-button>
            <el-button type="warning" @click="updateFunc(scope.row)">变更</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="page"
        :layout="PageLayout"
        :page-size="pageSize"
        :page-sizes="PageSizes"
        :total="total"
        @current-change="pageChange"
        @size-change="pageSizeChange"
      />
    </el-card>
    <el-dialog v-model="dialogShow" :before-close="closeDialog" :title="dTitle" destroy-on-close top="10px">
      <el-form ref="elFormRef" :disabled="dType === 'view'" :model="formData" :rules="formDataRule"
               label-position="right" label-width="80px">
        <el-form-item label="头像" prop="avatar">
          <UploadImg :img-url="formData.avatar" :file-remove="fileRemove" :upload-success="fileUploadSuccess"
                     :file-type="FileTypeAvatar"/>
        </el-form-item>
        <el-form-item label="手机号" prop="mobile">
          <el-input v-model="formData.mobile" :maxlength="16" clearable show-word-limit/>
        </el-form-item>
        <el-form-item label="昵称" prop="nickname">
          <el-input v-model="formData.nickname" :maxlength="16" clearable show-word-limit/>
        </el-form-item>
        <el-form-item label="区域" prop="retail_area_id">
          <el-select v-model="formData.retail_area_id" style="width:100%">
            <el-option v-for="item in userStore.areasList" :key="item.id" :label="item.name" :value="item.id"/>
          </el-select>
        </el-form-item>
        <!--<el-form-item label="密码" prop="password"><el-input v-model="formData.password" :maxlength="256" show-password/></el-form-item>-->
      </el-form>
      <template #footer>
        <div v-show="dType !== 'view'" class="dialog-footer">
          <el-button @click="closeDialog">取 消</el-button>
          <el-button type="primary" @click="enterDialog">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import {createUsers, deleteUsers, findUsers, getUsersList, updateUsers} from '@/api/retail/users'
import {formatDate} from '@/utils/format'
import {ElMessage, ElMessageBox} from 'element-plus'
import {onBeforeMount, reactive, ref} from 'vue'
import {FileTypeAvatar, PageLayout, PageSizes} from "@/utils/const"
import {useRoute, useRouter} from 'vue-router'
import {useUserStore} from "@/pinia/modules/user";
import {Plus} from "@element-plus/icons-vue";
import UploadImg from "@/components/upload/uploadImg.vue";

const path = import.meta.env.VITE_BASE_API
const userStore = useUserStore()
const router = useRouter()
const route = useRoute()
const upFileList = ref([])
const areasOptions = ref([])
const areasMap = ref({})
const dialogShow = ref(false)
const dTitle = ref('')
const dType = ref('')
const elFormRef = ref()
const page = ref(1)
const total = ref(0)
const pageSize = ref(100)
const tableData = ref([])
const searchInfo = ref({})
const formData = ref({
  avatar: '',
  mobile: '',
  nickname: '',
  open_id: '',
  union_id: '',
  retail_area_id: 0,
  password: '',
  login_ip: '',
  login_at: new Date(),
  active_at: new Date(),
})
const formDataRule = reactive({})

const fileUploadSuccess = (response, uploadFile) => {
  if (response.code === 0) {
    formData.value.avatar = response.data.url
    upFileList.value = [{
      name: response.data.key,
      url: response.data.url,
    }]
  }
}
const fileRemove = (uploadFile) => {
  formData.value.avatar = null
}

const sortChange = ({prop, order}) => {
  searchInfo.value.sort_prop = prop
  searchInfo.value.sort_desc = order !== 'ascending'
  getTableData()
}

const onReset = () => {
  searchInfo.value = {}
  getTableData()
}

const onSubmit = () => {
  page.value = 1
  getTableData()
}

const pageSizeChange = (val) => {
  pageSize.value = val
  getTableData()
}

const pageChange = (val) => {
  page.value = val
  getTableData()
}

const execItem = (item) => {
  item.created_at_str = formatDate(item.created_at)
  item.login_at_str = formatDate(item.login_at)
  item.active_at_str = formatDate(item.active_at)
  item.retail_area_str = userStore.GetAreaName(item.retail_area_id)

  return item
}

const getTableData = async () => {
  const table = await getUsersList({page: page.value, pageSize: pageSize.value, ...searchInfo.value})
  if (table.code === 0) {
    let resDataList = table.data.list || []
    let newList = []
    resDataList.forEach(item => {
      item = execItem(item)
      newList.push(item)
    })
    tableData.value = newList
    total.value = table.data.total
  }
}

const deleteRow = (row) => {
  ElMessageBox.confirm('确定删除?').then(() => {
    deleteInfoFunc(row)
  })
}

const initShowDialogInfo = async (row) => {
  const res = await findUsers({id: row.id})
  if (res.code === 0) {
    if (res.data.avatar !== '') {
      upFileList.value = [{
        name: res.data.avatar,
        url: res.data.avatar,
      }]
    }
    if (res.data.retail_area_id === 0) {
      res.data.retail_area_id = null
    }
    formData.value = res.data || {}
    dialogShow.value = true
  }
}

const viewFunc = async (row) => {
  dType.value = 'view'
  dTitle.value = '查看'
  initShowDialogInfo(row)
}

const updateFunc = async (row) => {
  dType.value = 'update'
  dTitle.value = '更新'
  initShowDialogInfo(row)
}

const deleteInfoFunc = async (row) => {
  const res = await deleteUsers({id: row.id})
  if (res.code === 0) {
    ElMessage({type: 'success', message: '删除成功'})
    getTableData()
  }
}

const openDialog = () => {
  dType.value = 'create'
  dTitle.value = '创建'
  dialogShow.value = true
}

const closeDialog = () => {
  dialogShow.value = false
  upFileList.value = []
  formData.value = {
    avatar: '',
    mobile: '',
    nickname: '',
    open_id: '',
    union_id: '',
    retail_area_id: 0,
    password: '',
    login_ip: '',
    login_at: new Date(),
    active_at: new Date(),
  }
}

const enterDialog = async () => {
  elFormRef.value?.validate(async (valid) => {
    if (!valid) return
    let res
    switch (dType.value) {
      case 'create':
        res = await createUsers(formData.value)
        break
      case 'update':
        res = await updateUsers(formData.value)
        break
      default:
        res = await createUsers(formData.value)
        break
    }
    if (res.code === 0) {
      ElMessage({type: 'success', message: '创建/更改成功'})
      closeDialog()
      getTableData()
    }
  })
}

onBeforeMount(() => {
  areasOptions.value = userStore.areasList
  getTableData()
})
</script>

<style></style>
