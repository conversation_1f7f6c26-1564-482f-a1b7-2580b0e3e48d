<template>
  <div>
    <el-card shadow="hover">
      <el-form :inline="true" :model="searchInfo" @keyup.enter="onSubmit">
        <el-form-item label="标题" prop="title">
          <el-input v-model="searchInfo.title" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="search" @click="onSubmit">查询</el-button>
          <el-button icon="refresh" @click="onReset">重置</el-button>
          <el-button type="success" icon="plus" @click="openDialog">新增</el-button>
          <el-button type="primary" icon="link" @click="getGenerateUrlLink">生成推广链接</el-button>
        </el-form-item>
      </el-form>
      <el-card>
        <el-form :model="notify">
          <!-- <el-row :gutter="24"> -->
            <!-- <el-col :span="18"> -->
              <el-form-item label="首页通知" prop="msg">
                <el-input v-model="notify.msg" :maxlength="64" />
              </el-form-item>
              <el-form-item label="团购通知" prop="group_buy_msg">
                <el-input v-model="notify.group_buy_msg" :maxlength="64" />
              </el-form-item>
              <el-form-item label="永丰通知" prop="yongfeng_msg">
                <el-input v-model="notify.yongfeng_msg" :maxlength="64" />
              </el-form-item>
              <el-form-item label="大兴通知" prop="daxing_msg">
                <el-input v-model="notify.daxing_msg" :maxlength="64" />
              </el-form-item>
            <!-- </el-col>
            <el-col :span="6"> -->
              <div style="text-align: center;">
                <el-button type="primary" @click="submitNotify">保存</el-button>
            </div>
            <!-- </el-col> -->
          <!-- </el-row> -->
        </el-form>
      </el-card>
      <el-table ref="tableRef" :data="tableData" row-key="id" border @sort-change="sortChange" show-overflow-tooltip>
        <el-table-column align="center" label="ID" prop="id" :width="100" fixed="left" />
        <el-table-column align="center" label="创建时间" prop="created_at_str" :width="160" />
        <el-table-column align="center" label="标题" prop="title" :min-width="120" />
        <el-table-column align="center" label="预览图地址" prop="preview_img" :width="120">
          <template #default="scope">
            <el-image :preview-src-list="[scope.row.preview_img_full]" :src="scope.row.preview_img_full" fit="contain" style="width: 30px; height: 30px"> </el-image>
          </template>
        </el-table-column>
        <el-table-column align="center" label="内容地址" :width="120">
          <template #default="scope">
            <el-button type="primary" link @click="openContentUrl(scope.row)">打开</el-button>
          </template>
        </el-table-column>
        <el-table-column align="center" label="操作" :width="280" fixed="right">
          <template #default="scope">
            <el-button type="primary" @click="viewFunc(scope.row)">查看详情</el-button>
            <el-button type="warning" @click="updateFunc(scope.row)">变更</el-button>
            <el-button type="danger" @click="deleteRow(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :layout="PageLayout"
        :current-page="page"
        :page-size="pageSize"
        :page-sizes="PageSizes"
        :total="total"
        @current-change="pageChange"
        @size-change="pageSizeChange"
      />
    </el-card>
    <el-dialog v-model="dialogShow" :before-close="closeDialog" :title="dTitle" top="10px" destroy-on-close>
      <el-form :model="formData" :disabled="dType === 'view'" label-position="right" ref="elFormRef" :rules="formDataRule" label-width="80px">
        <el-form-item label="标题" prop="title">
          <el-input v-model="formData.title" :maxlength="128" show-word-limit clearable />
        </el-form-item>
        <el-form-item label="预览图地址" prop="preview_img">
          <UploadImg :img-url="formData.preview_img" :file-remove="previewImgFileRemove" :upload-success="previewImgFileUploadSuccess" :file-type="FileTypeNotifyMsgPreviewImg" />
        </el-form-item>
        <el-form-item label="内容地址" prop="content_url">
          <UploadFile :file-url="formData.content_url" :file-remove="contentUrlFileRemove" :upload-success="contentUrlFileUploadSuccess" :file-type="FileTypeNotifyMsgContent" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer" v-show="dType !== 'view'">
          <el-button @click="closeDialog">取 消</el-button>
          <el-button type="primary" @click="enterDialog">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog v-model="centerDialogVisible" title="提示" width="500" center>
      <div>推广链接已生成,有效期30天,点击下方链接可复制链接到剪切板：</div>
      <div style="color: #409eff; font-size: 18px; margin-top: 6px" @click="copyText()">{{ miniProUrl }}</div>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="confirmCopy()"> 复制 </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { createNotifyMsg, deleteNotifyMsg, findNotifyMsg, getAppNotify, getNotifyMsgList, setAppNotify,setGroupBuyNotify, updateNotifyMsg, generateUrlLink } from '@/api/retail/notifyMsg'
import { formatDate, getOssFileUrl } from '@/utils/format'
import { ElMessage, ElMessageBox } from 'element-plus'
import { onBeforeMount, reactive, ref } from 'vue'
import { FileTypeNotifyMsgContent, FileTypeNotifyMsgPreviewImg, PageLayout, PageSizes } from '@/utils/const'
import { useRoute, useRouter } from 'vue-router'
import { useUserStore } from '@/pinia/modules/user'
import UploadImg from '@/components/upload/uploadImg.vue'
import UploadFile from '@/components/upload/uploadFile.vue'
import useClipboard from 'vue-clipboard3'

const userStore = useUserStore()
const router = useRouter()
const route = useRoute()
const dialogShow = ref(false)
const dTitle = ref('')
const dType = ref('')
const elFormRef = ref()
const page = ref(1)
const total = ref(0)
const pageSize = ref(100)
const tableData = ref([])
const searchInfo = ref({})
const notify = ref({ msg: '',daxing_msg:'',yongfeng_msg:'',group_buy_msg:'' })
const formData = ref({ title: '', preview_img: '', content_url: '' })
const formDataRule = reactive({
  title: [
    { required: true, message: '请完善该值', trigger: ['input', 'blur'] },
    { whitespace: true, message: '不能只输入空格', trigger: ['input', 'blur'] },
  ],
  preview_img: [
    { required: true, message: '请完善该值', trigger: ['input', 'blur'] },
    { whitespace: true, message: '不能只输入空格', trigger: ['input', 'blur'] },
  ],
  content_url: [
    { required: true, message: '请完善该值', trigger: ['input', 'blur'] },
    { whitespace: true, message: '不能只输入空格', trigger: ['input', 'blur'] },
  ],
})

const centerDialogVisible = ref(false)
const miniProUrl = ref('')
const getGenerateUrlLink = async () => {
  const rspData = await generateUrlLink({})
  if (rspData.code === 0) {
    centerDialogVisible.value = true
    miniProUrl.value = rspData.data
  }
}

const { toClipboard } = useClipboard()

const confirmCopy = async () => {
  copyText()
  centerDialogVisible.value = false
}

const copyText = async () => {
  try {
    // 复制
    await toClipboard(miniProUrl.value)
    ElMessage({
      type: 'success',
      message: '复制成功',
    })
    // 复制成功
  } catch (e) {
    console.log(e)
    // 复制失败
    ElMessage({
      type: 'warning',
      message: '复制失败',
    })
  }
}

const openContentUrl = (row) => {
  window.open(row.content_url_full, '_blank')
}
const previewImgFileUploadSuccess = (response, uploadFile) => {
  if (response.code === 0) {
    formData.value.preview_img = response.data.key
  }
}

const previewImgFileRemove = (uploadFile) => {
  formData.value.preview_img = null
}

const contentUrlFileUploadSuccess = (response, uploadFile) => {
  if (response.code === 0) {
    formData.value.content_url = response.data.key
  }
}

const contentUrlFileRemove = (uploadFile) => {
  formData.value.content_url = null
}

const sortChange = ({ prop, order }) => {
  searchInfo.value.sort_prop = prop
  searchInfo.value.sort_desc = order !== 'ascending'
  getTableData()
}

const onReset = () => {
  searchInfo.value = {}
  getTableData()
}

const onSubmit = () => {
  page.value = 1
  getTableData()
}

const pageSizeChange = (val) => {
  pageSize.value = val
  getTableData()
}

const pageChange = (val) => {
  page.value = val
  getTableData()
}

const execItem = (item) => {
  item.created_at_str = formatDate(item.created_at)
  item.preview_img_full = getOssFileUrl(item.preview_img)
  item.content_url_full = getOssFileUrl(item.content_url)
  return item
}

const getTableData = async () => {
  const table = await getNotifyMsgList({ page: page.value, page_size: pageSize.value, ...searchInfo.value })
  if (table.code === 0) {
    let resDataList = table.data.list || []
    let newList = []
    resDataList.forEach((item) => {
      item = execItem(item)
      newList.push(item)
    })
    tableData.value = newList
    total.value = table.data.total
  }
}

const deleteRow = (row) => {
  ElMessageBox.confirm('确定删除?').then(() => {
    deleteInfoFunc(row)
  })
}

const initShowDialogInfo = async (row) => {
  const res = await findNotifyMsg({ id: row.id })
  if (res.code === 0) {
    formData.value = res.data || {}
    dialogShow.value = true
  }
}

const viewFunc = async (row) => {
  dType.value = 'view'
  dTitle.value = '查看'
  initShowDialogInfo(row)
}

const updateFunc = async (row) => {
  dType.value = 'update'
  dTitle.value = '更新'
  initShowDialogInfo(row)
}

const deleteInfoFunc = async (row) => {
  const res = await deleteNotifyMsg({ id: row.id })
  if (res.code === 0) {
    ElMessage({ type: 'success', message: '删除成功' })
    getTableData()
  }
}

const openDialog = () => {
  dType.value = 'create'
  dTitle.value = '创建'
  dialogShow.value = true
}

const closeDialog = () => {
  dialogShow.value = false
  formData.value = { title: '', preview_img: '', content_url: '' }
}

const enterDialog = async () => {
  elFormRef.value?.validate(async (valid) => {
    if (!valid) return
    let res
    switch (dType.value) {
      case 'create':
        res = await createNotifyMsg(formData.value)
        break
      case 'update':
        res = await updateNotifyMsg(formData.value)
        break
      default:
        res = await createNotifyMsg(formData.value)
        break
    }
    if (res.code === 0) {
      ElMessage({ type: 'success', message: '创建/更改成功' })
      closeDialog()
      getTableData()
    }
  })
}

const initAppNotify = () => {
  getAppNotify().then((res) => {
    if (res.code === 0) {
      notify.value = res.data || {}
    }
  })
}

const submitNotify = () => {
  setAppNotify(notify.value).then((res) => {
    if (res.code === 0) {
      ElMessage({ type: 'success', message: '成功' })
    }
  })
}
onBeforeMount(() => {
  initAppNotify()
  getTableData()
})
</script>

<style></style>
