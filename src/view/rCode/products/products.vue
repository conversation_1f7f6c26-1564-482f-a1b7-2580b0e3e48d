<template>
  <div>
    <!-- <el-card> -->
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo" @keyup.enter="onSubmit">
        <el-form-item label="商品编码" prop="code">
          <el-input v-model="searchInfo.code" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="特价商品" prop="special_status">
          <el-select v-model="searchInfo.special_status" clearable placeholder="请选择">
            <el-option v-for="item in special_status_list" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="区域" prop="retail_area_id">
          <el-select v-model="searchInfo.retail_area_id" clearable placeholder="请选择" @change="initSearProductCategoriesOptions">
            <el-option v-for="item in userStore.areasList" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="分类类型" prop="cat_type">
          <el-select v-model="searchInfo.cat_type" clearable placeholder="请选择" @change="initSearProductCategoriesOptions">
            <el-option v-for="item in userStore.catTypeList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="商品分类" prop="product_category_id">
          <el-select v-model="searchInfo.product_category_id" clearable placeholder="请选择">
            <el-option v-for="item in categoriesOptions" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="商品名称" prop="name">
          <el-input v-model="searchInfo.name" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="上架时间" prop="dateRange">
          <el-date-picker v-model="searchInfo.dateRange" type="datetimerange" value-format="YYYY-MM-DDTHH:mm:ssZ" @change="onChangeDateRange1" />

          <!-- <el-select v-model="searchInfo.week_days" clearable placeholder="请选择">
            <el-option v-for="item in userStore.weekDayList" :key="item.value" :label="item.label" :value="item.value"/>
          </el-select> -->
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="search" @click="onSubmit">查询</el-button>
          <el-button icon="refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <div class="gva-btn-list">
        <el-button icon="plus" type="success" @click="openDialog">新增 </el-button>
        <el-button icon="DocumentAdd" type="success" @click="showImportExcel">导入 </el-button>
        <el-button :disabled="total < 1" icon="Download" type="success" @click="onExportNew">导出</el-button>

        <el-button icon="plus" type="success" @click="batchSetShelfTime('select')">批量设置上下架时间 </el-button>
        <el-button icon="plus" type="success" @click="batchSetShelfTime('condition')">按条件设置上下架时间 </el-button>

        <el-popover placement="left" :width="400" trigger="click" :visible="visible">
          <template #reference>
            <el-button type="primary" style="position:absolute;right:25px" icon="Setting" @click="visible = true">配置显示字段</el-button>
          </template>

          <!-- 配置列面板 -->
          <transition name="fade">
            <div>
              <div>选择显示字段</div>
              <div>
                <el-row>
                  <!-- <el-col :span="8"> <el-checkbox v-model="columnsList.id" disabled>ID</el-checkbox> </el-col> -->
                  <el-col :span="8"> <el-checkbox v-model="columnsList.code">商品编码</el-checkbox></el-col>
                  <el-col :span="8"> <el-checkbox v-model="columnsList.special_status">特价商品</el-checkbox> </el-col>
                  <el-col :span="8"> <el-checkbox v-model="columnsList.name">商品名称</el-checkbox></el-col>
                  <el-col :span="8"> <el-checkbox v-model="columnsList.shelves_switch">上架状态</el-checkbox></el-col>
                  <el-col :span="8"> <el-checkbox v-model="columnsList.shelves_days">上架日期</el-checkbox></el-col>
                  <el-col :span="8"> <el-checkbox v-model="columnsList.shelves_start_time">上架时间</el-checkbox></el-col>
                  <el-col :span="8"> <el-checkbox v-model="columnsList.image_url_full_list">图片地址</el-checkbox></el-col>
                  <el-col :span="8"> <el-checkbox v-model="columnsList.retail_area_id">区域</el-checkbox></el-col>
                  <el-col :span="8"> <el-checkbox v-model="columnsList.product_category_id">商品分类</el-checkbox></el-col>
                  <el-col :span="8"> <el-checkbox v-model="columnsList.cat_type">分类类型</el-checkbox></el-col>
                  <el-col :span="8"> <el-checkbox v-model="columnsList.purchase_price">商品进价</el-checkbox></el-col>
                  <el-col :span="8"> <el-checkbox v-model="columnsList.price">价格</el-checkbox></el-col>
                  <el-col :span="8"> <el-checkbox v-model="columnsList.sort">排序</el-checkbox></el-col>
                  <el-col :span="8"> <el-checkbox v-model="columnsList.stock">库存</el-checkbox></el-col>
                  <el-col :span="8"> <el-checkbox v-model="columnsList.unit">单位</el-checkbox></el-col>
                  <el-col :span="8"> <el-checkbox v-model="columnsList.sale">销售量</el-checkbox></el-col>
                  <el-col :span="8"> <el-checkbox v-model="columnsList.created_at">创建时间</el-checkbox></el-col>
                </el-row>
              </div>
            </div>
          </transition>
          <div style="text-align: right; margin: 20px 0 0">
            <el-button type="text" @click="visible = false">取消</el-button>
            <el-button type="primary" plain @click="saveColumn">保存</el-button>
          </div>
          <!-- <template #reference>
              <i
                class="el-icon-setting"
                style="font-size: 22px; cursor: pointer"
                @click="visible = true"
              ></i>
            </template> -->
        </el-popover>
      </div>
      <el-table ref="tableRef" :data="tableData" :max-height="680" border row-key="id" show-overflow-tooltip @sort-change="sortChange" @selection-change="handleSelectionChange">
        <el-table-column type="selection" :show-overflow-tooltip="false" align="center" width="55" />
        <el-table-column :width="100" align="center" fixed="left" label="ID" prop="id" />
        <el-table-column v-if="columnsList.code" :min-width="120" align="center" label="商品编码" prop="code" sortable="custom" />
        <el-table-column v-if="columnsList.special_status" :min-width="120" align="center" label="特价商品" prop="special_status" sortable="custom">
          <template #default="scope">
            {{ scope.row.special_status === 2 ? '是' : '否' }}
          </template>
        </el-table-column>
        <el-table-column v-if="columnsList.name" :min-width="240" align="center" label="商品名称" prop="name" />
        <el-table-column v-if="columnsList.shelves_switch" :width="120" align="center" label="上架状态" prop="shelves_switch" sortable="custom">
          <template #default="scope">
            <el-switch v-model="scope.row.shelves_switch" :active-value="1" :inactive-value="2" @change="updateShelvesSwitch(scope.row)" />
          </template>
        </el-table-column>
        <el-table-column v-if="columnsList.shelves_days" :width="180" align="center" label="上架日期" prop="shelves_days">
          <template #default="scope">
            <!-- {{ scope.row.buy_start_str }} -->
            <el-date-picker
              popper-class="data-rang-class"
              style="width: 100%"
              v-model="scope.row.shelves_days"
              type="dates"
              :clearable="false"
              :editable="true"
              :disabled-date="
                () => {
                  return true
                }
              "
            />
          </template>
        </el-table-column>
        <el-table-column v-if="columnsList.shelves_start_time" :width="180" align="center" label="上架时间" prop="shelves_start_time">
          <template #default="scope"> {{ scope.row.shelves_start_time }} - {{ scope.row.shelves_end_time }} </template>
        </el-table-column>
        <!-- <el-table-column v-if="columnsList.buy_end" :width="180" align="center" label="下架时间" prop="buy_end" sortable="custom">
          <template #default="scope">
            {{ scope.row.buy_end_str }}
          </template>
        </el-table-column> -->
        <el-table-column v-if="columnsList.image_url_full_list" :width="90" align="center" label="图片地址" prop="image_url_full_list" class-name="img-col">
          <template #default="scope">
            <el-image
              :preview-src-list="scope.row.image_url_full_list"
              :src="scope.row.image_url_full_list.length > 0 ? scope.row.image_url_full_list[0] : ''"
              fit="contain"
              style="width: 30px; height: 30px"
            />
          </template>
        </el-table-column>
        <el-table-column v-if="columnsList.retail_area_id" :min-width="120" align="center" label="区域" prop="retail_area_id" sortable="custom">
          <template #default="scope">
            {{ userStore.GetAreaName(scope.row.retail_area_id) }}
          </template>
        </el-table-column>
        <el-table-column v-if="columnsList.product_category_id" :min-width="120" align="center" label="商品分类" prop="product_category_id" sortable="custom">
          <template #default="scope">
            {{ allCategoriesMap[scope.row.product_category_id] }}
          </template>
        </el-table-column>
        <el-table-column v-if="columnsList.cat_type" :min-width="120" align="center" label="分类类型" prop="cat_type" sortable="custom">
          <template #default="scope">
            {{ userStore.GetCatTypeName(scope.row.cat_type) }}
          </template>
        </el-table-column>
        <el-table-column v-if="columnsList.purchase_price" :min-width="120" align="center" label="商品进价" prop="purchase_price" sortable="custom" />
        <el-table-column v-if="columnsList.price" :width="80" align="center" label="价格" prop="price" sortable="custom" />
        <el-table-column v-if="columnsList.sort" :width="120" align="center" label="排序" prop="sort" sortable="custom" />
        <el-table-column v-if="columnsList.unit" :width="70" align="center" label="单位" prop="unit" />
        <el-table-column v-if="columnsList.default_stock" :width="130" align="center" label="默认库存" prop="default_stock" sortable="custom" />
        <el-table-column v-if="columnsList.stock_update_hour" :width="160" align="center" label="库存更新小时" prop="stock_update_hour" sortable="custom" />
        <el-table-column v-if="columnsList.stock" :width="100" align="center" label="库存" prop="stock" sortable="custom">
          <template #default="{ row }">
            <el-input-number v-model.trim="row.stock" class="storeClass" :min="0" @change="updateFunc(row, 'store')" @click="copyStock(row.stock)" />
          </template>
        </el-table-column>

        <el-table-column v-if="columnsList.sale" :width="100" align="center" label="销售量" prop="sale" sortable="custom" />
        <el-table-column v-if="columnsList.created_at" :width="160" align="center" label="创建时间" prop="created_at" sortable="custom">
          <template #default="scope">
            {{ scope.row.created_at_str }}
          </template>
        </el-table-column>
        <el-table-column :width="260" align="center" fixed="right" label="操作">
          <template #default="scope">
            <el-button type="primary" @click="viewFunc(scope.row)">查看详情</el-button>
            <el-button type="warning" @click="updateFunc(scope.row)">变更</el-button>
            <el-button type="danger" @click="deleteRow(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="page"
        :layout="PageLayout"
        :page-size="pageSize"
        :page-sizes="PageSizes"
        :total="total"
        @current-change="pageChange"
        @size-change="pageSizeChange"
      />
      <!-- </el-card> -->
    </div>
    <el-dialog v-model="dialogShow" :before-close="closeDialog" :title="dTitle" destroy-on-close top="10px">
      <el-form ref="elFormRef" :disabled="dType === 'view'" :model="formData" :rules="formDataRule" label-position="right" label-width="80px">
        <el-form-item label="商品编码" prop="code">
          <el-input v-model="formData.code" :maxlength="16" clearable show-word-limit />
        </el-form-item>

        <el-form-item label="区域" prop="retail_area_id">
          <el-select v-model="formData.retail_area_id" :disabled="dType !== 'create'" style="width: 100%" @change="initProductCategoriesOptions">
            <el-option v-for="item in userStore.areasList" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="分类类型" prop="cat_type">
          <el-select v-model="formData.cat_type" :disabled="dType !== 'create'" style="width: 100%" @change="initProductCategoriesOptions">
            <el-option v-for="item in userStore.catTypeList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="商品分类" prop="product_category_id">
          <el-select v-model="formData.product_category_id" :disabled="dType !== 'create'" style="width: 100%">
            <el-option v-for="item in categoriesOptions" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="商品名称" prop="name">
          <el-input v-model="formData.name" :maxlength="32" clearable show-word-limit />
        </el-form-item>
        <el-form-item label="商品描述" prop="describe">
          <el-input v-model="formData.describe" :autosize="{ minRows: 3, maxRows: 5 }" type="textarea" :maxlength="256" show-word-limit />
        </el-form-item>
        <el-form-item label="图片地址" prop="image_url">
          <UploadImgList
            :key="currentNumber"
            ref="uploadImgList"
            :file-remove="fileRemove"
            :file-type="formData.cat_type === CatTypeTakeaway ? FileTypeTakeaway : FileTypeGroupBuy"
            :img-url-list="formData.image_url_list"
            :file-upload-success="fileUploadSuccess"
            :limit="10"
            @updateImageList="updateImageList"
          />
        </el-form-item>
        <el-form-item label="特价商品" prop="special_status">
          <el-switch v-model="formData.special_status" :active-value="2" :inactive-value="1" />
        </el-form-item>
        <el-form-item v-if="formData.special_status == 2" label="商品原价" prop="origin_price">
          <el-input-number v-model="formData.origin_price" :precision="2" style="width: 100%" />
        </el-form-item>
        <el-form-item label="商品进价" prop="purchase_price">
          <el-input-number v-model="formData.purchase_price" :precision="2" style="width: 100%" />
        </el-form-item>
        <el-form-item label="价格" prop="price">
          <el-input-number v-model="formData.price" :precision="2" style="width: 100%" />
        </el-form-item>
        <el-form-item label="单位" prop="unit">
          <el-select v-model="formData.unit" filterable allow-create default-first-option style="width: 100%">
            <el-option v-for="item in Units" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item label="库存" prop="stock">
          <el-input-number v-model="formData.stock" style="width: 100%" />
        </el-form-item>
        <el-form-item label="默认库存" prop="default_stock">
          <el-input-number v-model="formData.default_stock" :min="1" style="width: 100%" />
        </el-form-item>
        <el-form-item :label-width="120" label="库存更新小时" prop="stock_update_hour">
          <el-select v-model="formData.stock_update_hour" style="width: 100%">
            <el-option v-for="item in [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23]" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item label="销售量" prop="sale">
          <el-input-number v-model="formData.sale" style="width: 100%" :disabled="true" />
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input-number v-model="formData.sort" style="width: 100%" />
        </el-form-item>
        <el-form-item label="是否上架" prop="shelves_switch">
          <el-switch v-model="formData.shelves_switch" :active-value="1" :inactive-value="2" />
        </el-form-item>
        <el-form-item label="上架日期" prop="shelves_days">
          <!-- <el-checkbox-group
              v-model="week"
              size="large"
              @change="handleCheckedChange"
          >
            <el-checkbox-button label="1">一</el-checkbox-button>
            <el-checkbox-button label="2">二</el-checkbox-button>
            <el-checkbox-button label="3">三</el-checkbox-button>
            <el-checkbox-button label="4">四</el-checkbox-button>
            <el-checkbox-button label="5">五</el-checkbox-button>
          </el-checkbox-group>

          <el-checkbox
              v-model="checkAll"
              :indeterminate="isIndeterminate"
              @change="handleCheckAllChange"
              style="margin-left: 10px"
          >全选
          </el-checkbox
          > -->

          <!-- <el-date-picker
            v-model="formData.dateRange"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            date-format="YYYY/MM/DD"
            value-format="YYYY-MM-DD"
            @change="onChangeDateRange"
          /> -->
          <el-date-picker style="width: 100%" v-model="formData.shelves_days" :shortcuts="shortcuts1" type="dates" value-format="YYYY-MM-DD" placeholder="请选择日期" />
        </el-form-item>
        <el-form-item label="上架时间" prop="shelves_timeRange">
          <!-- <el-time-picker
            v-model="formData.timeRange"
            is-range
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="HH:mm:ss"
            value-format="HH:mm:ss"
            @change="onChangeTimeRange"
          /> -->

          <!-- <div class="demo-time-range" style="width: 100%">
            <el-time-select
              v-model="formData.shelves_start_time"
              style="width: 49%"
              :max-time="formData.shelves_end_time"
              class="mr-4"
              placeholder="开始时间"
              start="00:00"
              step="00:60"
              end="23:00"
              value-format="HH:mm:ss"
              format="HH:mm:ss"
            />
            <el-time-select
              v-model="formData.shelves_end_time"
              style="width: 49%"
              value-format="HH:mm:ss"
              format="HH:mm:ss"
              :min-time="formData.shelves_start_time"
              placeholder="结束时间"
              start="00:00"
              step="00:60"
              end="23:00"
            />
          </div> -->

          <el-time-picker
            v-model="formData.shelves_timeRange"
            is-range
            range-separator="To"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="HH:mm:ss"
            :default-value="defaultTime"
          />
        </el-form-item>

        <!--<el-form-item label="限购" prop="buy_limit">-->
        <!--  <el-input-number v-model="formData.buy_limit" style="width:100%"/>-->
        <!--</el-form-item>-->
      </el-form>
      <template #footer>
        <div v-show="dType !== 'view'" class="dialog-footer">
          <el-button @click="closeDialog">取 消</el-button>
          <el-button type="primary" @click="enterDialog">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog v-model="importDialogVisible" :before-close="closeImportDialog" title="数据导入" top="10px" width="70%">
      <div class="flex justify-around items-center">
        <el-upload v-model:file-list="fileList" :before-upload="beforeUpload" accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" action="" drag>
          <el-icon class="el-icon--upload">
            <upload-filled />
          </el-icon>
          <div class="el-upload__text">拖拽文件或者点击上传</div>
          <template #tip><p style="color: #ff0000">为了方便获取表头信息,第一行必须所有参数都要填写</p></template>
        </el-upload>

        <div class="flex flex-col items-center">
          <el-button size="small" type="primary" @click="downloadExcelDemo">下载示例文件 </el-button>
          <el-button :disabled="importTableData.length === 0 || !importValid" style="margin: 40px 0 0 0; width: 180px" type="success" @click="submitExcelImport">上传 </el-button>
        </div>
      </div>
      <el-table ref="importTableDataRef" :data="importTableData" :max-height="590" border show-overflow-tooltip>
        <el-table-column v-for="(item, index) in importTableColumns" :key="index" :label="item.label" :min-width="170" :prop="item.prop" align="center" />
      </el-table>
    </el-dialog>

    <el-dialog v-model="dialogShowSet" :before-close="closeDialogSet" title="设置" destroy-on-close top="10px">
      <el-form ref="elFormRefSet" :model="formDataSet" :rules="formDataRuleSet" label-position="right" label-width="80px">
        <div v-if="batchType === 'condition'">
          <el-form-item label="区域" prop="retail_area_id">
            <el-select v-model="formDataSet.retail_area_id" style="width: 100%" clearable placeholder="请选择" @change="initSearProductCategoriesOptions">
              <el-option v-for="item in userStore.areasList" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="分类类型" prop="cat_type">
            <el-select v-model="formDataSet.cat_type" clearable style="width: 100%" placeholder="请选择" @change="initSearProductCategoriesOptions">
              <el-option v-for="item in userStore.catTypeList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="商品分类" prop="product_category_id">
            <el-select v-model="formDataSet.product_category_id" style="width: 100%" clearable placeholder="请选择">
              <el-option v-for="item in categoriesOptions" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
        </div>

        <el-form-item label="上架日期" prop="shelves_days">
          <!-- <el-date-picker
            v-model="formDataSet.dateRange"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            date-format="YYYY/MM/DD"
            value-format="YYYY-MM-DD"
            @change="onChangeDateRangeSet"
          /> -->

          <el-date-picker style="width: 100%" v-model="formDataSet.shelves_days" :shortcuts="shortcuts" type="dates" value-format="YYYY-MM-DD" placeholder="请选择日期" />
        </el-form-item>
        <el-form-item label="上架时间" prop="shelves_timeRange">
          <!-- <el-time-picker
            v-model="formDataSet.timeRange"
            is-range
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="HH:mm:ss"
            value-format="HH:mm:ss"
            @change="onChangeTimeRangeSet"
          /> -->

          <!-- <div class="demo-time-range" style="width: 100%"> -->

          <el-time-picker
            v-model="formDataSet.shelves_timeRange"
            is-range
            range-separator="To"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="HH:mm:ss"
            :default-value="defaultTime"
          />
          <!-- 
            <el-time-select
              v-model="formDataSet.shelves_start_time"
              style="width: 45%"
              :max-time="formDataSet.shelves_end_time"
              class="mr-4"
              placeholder="开始时间"
              start="00:00:00"
              step="00:01"
              end="23:59:59"
              value-format="HH:mm:ss"
              format="HH:mm:ss"
            />
            <el-time-select
              v-model="formDataSet.shelves_end_time"
              value-format="HH:mm:ss"
              format="HH:mm:ss"
              style="width: 45%"
              :min-time="formDataSet.shelves_start_time"
              placeholder="结束时间"
              start="00:00:00"
              step="00:60"
              end="23:59:59"
            /> -->
          <!-- </div> -->
        </el-form-item>

        <!--<el-form-item label="限购" prop="buy_limit">-->
        <!--  <el-input-number v-model="formData.buy_limit" style="width:100%"/>-->
        <!--</el-form-item>-->
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeDialogSet">取 消</el-button>
          <el-button type="primary" @click="batchSetSubmit">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import {
  createProducts,
  deleteProducts,
  findProducts,
  getProductsList,
  importProducts,
  updateProducts,
  batchUpdateProductsTime,
  batchUpdateProductsTimeOnWhere,
} from '@/api/retail/products'
import { formatDate, getOssFileUrlList } from '@/utils/format'
import { ElMessage, ElMessageBox, dayjs } from 'element-plus'
import { onBeforeMount, reactive, ref } from 'vue'
import { CatTypeTakeaway, FileTypeGroupBuy, FileTypeTakeaway, PageLayout, PageSizes, Units } from '@/utils/const'
import { useRoute, useRouter } from 'vue-router'
import { useUserStore } from '@/pinia/modules/user'
import UploadImgList from '@/components/upload/uploadImgList.vue'
import { getProductCategoriesAll, productCategoriesAll } from '@/api/retail/productCategories'
import { UploadFilled } from '@element-plus/icons-vue'
import * as XLSX from 'xlsx'
import FileSaver from 'file-saver'
import ExcelJS from 'exceljs'

import { generateLastWeekDates, generateNextMonthDates, generateNextHalfYearDates, generateNextYearDates } from '@/utils/date'

const columnsList = ref({
  // id: true,
  code: true,
  special_status: true,
  name: true,
  shelves_switch: true,
  buy_start: true,
  buy_end: true,
  image_url_full_list: true,
  retail_area_id: true,
  product_category_id: true,
  cat_type: true,
  purchase_price: true,
  price: true,
  sort: true,
  stock: true,
  unit: true,
  sale: true,
  created_at: true,
  shelves_days: true,
  shelves_start_time: true,
})
const visible = ref(false)

const dialogShowSet = ref(false)
const formDataSet = ref({
  shelves_days: [],
  shelves_timeRange: [],
  // shelves_end_time: '',
})
const batchType = ref('select')
const defaultTime = [new Date('2024-01-01 00:00:00'), new Date('2024-01-01 18:00:00')]

const shortcuts = [
  {
    text: '未来一周',
    value: () => {
      formDataSet.value.shelves_days = generateLastWeekDates()
    },
  },
  {
    text: '未来一月',
    value: () => {
      formDataSet.value.shelves_days = generateNextMonthDates()
    },
  },
  {
    text: '未来半年',
    value: () => {
      formDataSet.value.shelves_days = generateNextHalfYearDates()
    },
  },
  {
    text: '未来一年',
    value: () => {
      formDataSet.value.shelves_days = generateNextYearDates()
    },
  },
]

const shortcuts1 = [
  {
    text: '未来一周',
    value: () => {
      formData.value.shelves_days = generateLastWeekDates()
    },
  },
  {
    text: '未来一月',
    value: () => {
      formData.value.shelves_days = generateNextMonthDates()
    },
  },
  {
    text: '未来半年',
    value: () => {
      formData.value.shelves_days = generateNextHalfYearDates()
    },
  },
  {
    text: '未来一年',
    value: () => {
      formData.value.shelves_days = generateNextYearDates()
    },
  },
]

const formDataRuleSet = reactive({
  shelves_days: [{ required: true, message: '请完善该值', trigger: ['input', 'blur'] }],
  shelves_timeRange: [{ required: true, message: '请完善该值', trigger: ['input', 'blur'] }],
  // shelves_start_time: [{ required: true, message: '请完善该值', trigger: ['input', 'blur'] }],
})

const saveColumn = () => {
  localStorage.setItem('products_columnSet', JSON.stringify(columnsList.value))
  visible.value = false
}
const multipleSelection = ref([])

const handleSelectionChange = (val) => {
  multipleSelection.value = val
}

// const onChangeDateRangeSet = (val) => {
//   formDataSet.value.buy_start = val[0]
//   formDataSet.value.buy_end = val[1]
// }
// const onChangeTimeRangeSet = (val) => {
//   formDataSet.value.buy_start_time = val[0]
//   formDataSet.value.buy_end_time = val[1]
// }
const batchSetShelfTime = (type) => {
  batchType.value = type
  if (type === 'select') {
    if (multipleSelection.value.length === 0) {
      ElMessage.error('请选择商品')
      return
    }
  }
  dialogShowSet.value = true
}
const batchSetSubmit = () => {
  elFormRefSet.value?.validate(async (valid) => {
    if (!valid) return

    if (batchType.value === 'select') {
      const ids = multipleSelection.value.map((item) => item.id)

      const param = {
        ids: ids,
        shelves_days: formDataSet.value.shelves_days,
        shelves_start_time: formDataSet.value.shelves_timeRange[0],
        shelves_end_time: formDataSet.value.shelves_timeRange[1],
      }

      console.log(param)

      batchUpdateProductsTime(param).then((res) => {
        if (res.code === 0) {
          ElMessage.success('批量设置成功')
          dialogShowSet.value = false
          closeDialogSet()
          getTableData()
        } else {
          ElMessage.error(res.msg)
        }
      })
    } else {
      const params = {
        retail_area_id: searchInfo.value.retail_area_id,
        product_category_id: searchInfo.value.product_category_id,
        cat_type: searchInfo.value.cat_type,
        shelves_switch: searchInfo.value.shelves_switch,
        shelves_days: formDataSet.value.shelves_days,
        shelves_start_time: formDataSet.value.shelves_timeRange[0],
        shelves_end_time: formDataSet.value.shelves_timeRange[1],
      }

      console.log(params)

      batchUpdateProductsTimeOnWhere(params).then((res) => {
        if (res.code === 0) {
          ElMessage.success('批量设置成功')
          dialogShowSet.value = false
          closeDialogSet()
          getTableData()
        } else {
          ElMessage.error(res.msg)
        }
      })
    }
  })
}

const closeDialogSet = () => {
  dialogShowSet.value = false
  formDataSet.value = {
    shelves_days: [],
    // timeRange: [],
    shelves_start_time: '',
    shelves_end_time: '',
    retail_area_id: '',
    cat_type: '',
    product_category_id: '',
  }

  categoriesOptions.value = []
}

const userStore = useUserStore()
const router = useRouter()
const route = useRoute()
const allCategoriesOptions = ref([])
const allCategoriesMap = ref({})
const categoriesOptions = ref([])
const importDialogVisible = ref(false)
const importTableData = ref([])
const importReqData = ref([])
const importTableColumns = ref([])
const importValid = ref(true)
const fileList = ref([])
const dialogShow = ref(false)
const dTitle = ref('')
const dType = ref('')
const elFormRef = ref()
const elFormRefSet = ref()
const page = ref(1)
const total = ref(0)
const pageSize = ref(100)
const tableData = ref([])
const searchInfo = ref({})
const isNumber = /^[0-9]\d*$/
const copyStockNumber = ref(0)
const uploadImgList = ref(null)
const currentNumber = ref(0)
const weekDays = ref([])
const formData = ref({
  retail_area_id: null,
  cat_type: 1,
  product_category_id: null,
  name: '',
  image_url: '',
  price: 10,
  unit: '个',
  stock: 100,
  sale: 0,
  buy_limit: 0,
  shelves_switch: 0,
  // week_days: [],
})
const special_status_list = [
  {
    label: '是',
    value: '2',
  },
  {
    label: '否',
    value: '1',
  },
]
const week = ref(['1', '2', '3', '4', '5'])
const weekList = ['1', '2', '3', '4', '5']
const checkAll = ref(false) // 全选
const isIndeterminate = ref(true) // 全选中间态
const formDataRule = reactive({
  // retail_area_id: [
  //   { required: true, message: '请完善该值', trigger: ['input', 'blur'] },
  // ],
  // code: [
  //   { required: true, message: '请完善该值', trigger: ['input', 'blur'] },
  // ],
  cat_type: [{ required: true, message: '请完善该值', trigger: ['input', 'blur'] }],
  product_category_id: [{ required: true, message: '请完善该值', trigger: ['input', 'blur'] }],
  name: [
    { required: true, message: '请完善该值', trigger: ['input', 'blur'] },
    { whitespace: true, message: '不能只输入空格', trigger: ['input', 'blur'] },
  ],
  // image_url: [{required: true, message: '请完善该值', trigger: ['input', 'blur']},],
  price: [{ required: true, message: '请完善该值', trigger: ['input', 'blur'] }],
  purchase_price: [{ required: true, message: '请完善该值', trigger: ['input', 'blur'] }],
  origin_price: [{ required: true, message: '请完善该值', trigger: ['input', 'blur'] }],
  unit: [
    { required: true, message: '请完善该值', trigger: ['input', 'blur'] },
    { whitespace: true, message: '不能只输入空格', trigger: ['input', 'blur'] },
  ],
  stock: [{ required: true, message: '请完善该值', trigger: ['input', 'blur'] }],
  shelves_days: [{ required: true, message: '请完善该值', trigger: ['input', 'blur'] }],
  shelves_timeRange: [{ required: true, message: '请完善该值', trigger: ['input', 'blur'] }],
  // buy_limit: [{required: true, message: '请完善该值', trigger: ['input', 'blur']},],
})

const onChangeDateRange1 = (val) => {
  searchInfo.value.buy_start = val[0]
  searchInfo.value.buy_end = val[1]
}
const onChangeDateRange = (val) => {
  formData.value.buy_start = val[0]
  formData.value.buy_end = val[1]
}
const onChangeTimeRange = (val) => {
  formData.value.buy_start_time = val[0]
  formData.value.buy_end_time = val[1]
}

const submitExcelImport = () => {
  if (importReqData.value.length === 0) {
    ElMessage.error('上传的文件并没有内容,请您打开浏览器控制台查看是否有报错.')
    return
  }
  importProducts({ data: importReqData.value }).then((res) => {
    if (res.code === 0) {
      ElMessage.success('上传成功')
      closeImportDialog()
      getTableData()
    } else {
      ElMessage.error(res.msg)
    }
  })
}

const downloadExcelDemo = () => {
  window.open('https://zcstatic.costnovel.com/public/other/%E4%BA%A7%E5%93%81%E5%AF%BC%E5%85%A5%E7%A4%BA%E4%BE%8B%E7%AC%AC%E4%B8%89%E7%89%88.xlsx', '_blank')
}

const onExportNew = async () => {
  const table = await getProductsList({
    page: 1,
    pageSize: 20000,
    ...searchInfo.value,
  })
  if (table.code === 0) {
    const resDataLists = table.data.list || []
    const newList = []
    resDataLists.forEach((item) => {
      item = execItem(item)
      newList.push(item)
    })
    console.log(resDataLists)
    exportData(resDataLists)
    // tableData.value = newList
    // total.value = table.data.total
  }
}

const exportData = async (tableData) => {
  const workbook = new ExcelJS.Workbook()
  const sheet = workbook.addWorksheet('商品明细')
  sheet.columns = [
    { header: 'ID', key: 'id' },
    { header: '商品编码', key: 'code' },
    { header: '特价商品', key: 'special_status' },
    { header: '商品原价', key: 'origin_price' },
    { header: '商品名称', key: 'name' },
    { header: '上架状态', key: 'shelves_switch' },
    { header: '上架日期', key: 'shelves_days' },
    // { header: '上架时间', key: 'shelves_time' },
    { header: '上架时间', key: 'shelves_start_time' },
    { header: '下架时间', key: 'shelves_end_time' },
    // { header: '下架时间', key: 'buy_end_str' },
    // { header: '图片地址', key: 'image_url_full_list' },
    { header: '区域', key: 'regionName' },
    { header: '商品分类', key: 'product_category_id_name' },
    { header: '分类类型', key: 'cat_type_name' },
    { header: '商品进价', key: 'purchase_price' },
    { header: '价格', key: 'price' },
    { header: '排序', key: 'sort' },
    { header: '单位', key: 'unit' },
    // { header: '周几', key: 'week_days' },
    { header: '库存', key: 'stock' },
    { header: '销售量', key: 'sale' },
    { header: '创建时间', key: 'created_at_str' },
  ]
  // 批量设置
  // sheet.addRows(tableData.value)
  for (let i = 0; i < tableData.length; i++) {
    const rowIndex = i + 2
    const item = tableData[i]
    item.shelves_switch = item.shelves_switch === 1 ? '上架' : '下架'
    item.special_status = item.special_status === 2 ? '是' : '否'
    item.shelves_time = item.shelves_start_time + '-' + item.shelves_end_time
    item.regionName = userStore.GetAreaName(item.retail_area_id)
    item.cat_type_name = userStore.GetCatTypeName(item.cat_type)
    item.product_category_id_name = allCategoriesMap.value[item.product_category_id]
    sheet.addRow(item)
    // if (item.image_url_full_list.length > 0) {
    //   let colIndex = 'H'
    //   for (let j = 0; j < item.image_url_full_list.length; j++) {
    //     let imgItem = item.image_url_full_list[j]
    //     let img_full = getOssFileUrl(imgItem)
    //     let imgB64 = await convertImgToBase64(img_full)
    //     const imageId = workbook.addImage({
    //       base64: imgB64,
    //       extension: 'png',
    //     })
    //     let iRange = `${colIndex}${rowIndex}:${colIndex}${rowIndex}`
    //     sheet.addImage(imageId, iRange)
    //     colIndex = String.fromCharCode(colIndex.charCodeAt() + 1)
    //   }
    // }
  }

  // 生成 Excel 文件并保存
  const buffer = await workbook.xlsx.writeBuffer()
  try {
    FileSaver.saveAs(
      new Blob([buffer], {
        type: 'application/octet-stream',
      }),
      `用户反馈${new Date().valueOf()}.xlsx`
    )
  } catch (e) {
    console.log(e)
  }
}

const convertImgToBase64 = (imgUrl) => {
  return new Promise((resove, reject) => {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    const img = new Image()
    img.crossOrigin = 'Anonymous'
    img.onload = function () {
      canvas.height = img.height
      canvas.width = img.width
      ctx.drawImage(img, 0, 0, img.width, img.height)
      var dataURL = canvas.toDataURL('image/png')
      resove(dataURL)
    }
    img.onerror = function () {
      ElMessage.error(`图片加载异常,地址${imgUrl}`)
      reject(new Error('图片流异常'))
    }
    img.src = imgUrl
  })
}

const beforeUpload = (file) => {
  importTableData.value = []
  importTableColumns.value = []
  const reader = new FileReader()
  reader.readAsBinaryString(file)
  reader.onload = (evt) => {
    const data = evt.target.result
    const wb = XLSX.read(data, { type: 'binary' })
    const ws = wb.Sheets[wb.SheetNames[0]]
    const jsonData = XLSX.utils.sheet_to_json(ws)
    if (jsonData.length > 0) {
      const columns = Object.keys(jsonData[0])
      const tableColumns = columns.map((item) => {
        return {
          label: item,
          prop: item,
        }
      })
      importTableColumns.value = tableColumns
      importTableData.value = jsonData
      checkExcelDataNew()
    }
  }
  return false
}

const checkExcelData = () => {
  if (importTableData.value.length === 0) {
    return
  }
  const reqImportData = []
  for (let i = 0; i < importTableData.value.length; i++) {
    const item = importTableData.value[i]
    const itemWeekVal = `${item['周几']}`
    let itemWeekValList
    if (itemWeekVal.indexOf(',') !== -1) {
      itemWeekValList = itemWeekVal.split(',')
    } else if (itemWeekVal.indexOf('，') !== -1) {
      itemWeekValList = itemWeekVal.split('，')
    } else {
      itemWeekValList = [parseInt(itemWeekVal)]
    }
    const itemWeekIntList = []
    itemWeekValList.forEach((val) => {
      itemWeekIntList.push(+val)
    })
    reqImportData.push({
      retail_area_str: item['区域名称'],
      product_category_str: item['商品分类'],
      cat_type_str: item['分类类型'],
      name: item['商品名称'],
      describe: item['商品描述'],
      price: item['价格'],
      unit: item['单位'],
      stock: parseInt(item['库存']),
      weekdays: itemWeekIntList,
      shelves_switch_str: item['是否上架'],
    })
  }
  importReqData.value = reqImportData
}

const checkExcelDataNew = () => {
  if (importTableData.value.length === 0) {
    return
  }
  const reqImportData = []
  for (let i = 0; i < importTableData.value.length; i++) {
    const item = importTableData.value[i]
    const shelves_switch = item['是否上架'] === '是' ? 1 : 2
    const special_status = item['特价商品'] === '是' ? 2 : 1

    reqImportData.push({
      retail_area_str: item['区域名称'],
      product_category_str: item['商品分类'],
      cat_type_str: item['分类类型'],
      name: item['商品名称'],
      describe: item['商品描述'],
      price: item['价格'],
      code: item['商品编码'],
      origin_price: item['商品原价'],
      purchase_price: item['商品进价'],
      special_status: special_status,
      // buy_start: item['上架开始时间'],
      // buy_end: item['上架结束时间'],
      shelves_days: item['上架天'].split(','),
      shelves_start_time: item['上架开始小时'],
      shelves_end_time: item['上架结束小时'],
      unit: item['单位'],
      stock: parseInt(item['库存']),
      shelves_switch: shelves_switch,
    })
  }
  importReqData.value = reqImportData
}

const closeImportDialog = () => {
  importDialogVisible.value = false
  importTableData.value = []
  importTableColumns.value = []
}

const showImportExcel = () => {
  importDialogVisible.value = true
}

const fileUploadSuccess = (response, uploadFile) => {
  if (response.code === 0) {
    const iList = formData.value.image_url_list || []
    iList.push(response.data.key)
    formData.value.image_url_list = iList
    formData.value.image_url = iList[0]
  }
  currentNumber.value += 1
}

const updateImageList = (list) => {
  formData.value.image_url = list[0].name
  formData.value.image_url_list = []
  list.forEach((item) => {
    formData.value.image_url_list.push(item.name)
  })
}

const fileRemove = (uploadFile) => {
  let imgKey
  if (uploadFile.response) {
    imgKey = uploadFile.response.data.key
  } else {
    imgKey = uploadFile.name
  }
  // 从formData.value.image_url里面删除imgKey
  const index = formData.value.image_url_list.indexOf(imgKey)
  if (index > -1) {
    formData.value.image_url_list.splice(index, 1)
  }
}

const updateShelvesSwitch = async (row) => {
  row.week_days = row.week_days.split(',')
  row.week_days.forEach((item, index) => {
    row.week_days[index] = +item
  })
  const res = await updateProducts(row)
  if (res.code === 0) {
    ElMessage({ type: 'success', message: '成功' })
    getTableData()
  }
}

const sortChange = ({ prop, order }) => {
  searchInfo.value.sort_prop = prop
  searchInfo.value.sort_desc = order !== 'ascending'
  getTableData()
}

const onReset = () => {
  searchInfo.value = {}
  getTableData()
}

const onSubmit = () => {
  page.value = 1
  getTableData()
}

const pageSizeChange = (val) => {
  pageSize.value = val
  getTableData()
}

const pageChange = (val) => {
  page.value = val
  getTableData()
}

// 全选逻辑
const handleCheckAllChange = (val) => {
  week.value = val ? weekList : []
  isIndeterminate.value = false
}

// 多选
const handleCheckedChange = (value) => {
  const checkedCount = value.length
  checkAll.value = checkedCount === weekList.length
  isIndeterminate.value = checkedCount > 0 && checkedCount < weekList.length
}

const execItem = (item) => {
  item.created_at_str = formatDate(item.created_at)
  item.buy_start_str = formatDate(item.buy_start)
  item.buy_end_str = formatDate(item.buy_end)
  item.cat_type_str = userStore.GetCatTypeName(item.cat_type)
  item.image_url_full_list = getOssFileUrlList(item.image_url_list)
  // if (!item.week_days) {
  //   week.value = []
  // } else {
  //   week.value = item.week_days
  //   if (item.week_days[0] !== 0) {
  //     item.week_days.sort(function(num1, num2) {
  //       return num1 - num2
  //     })
  //   } else {
  //     item.week_days = weekList.join(',')
  //   }
  // }
  // week.value.forEach((item, index) => {
  //   week.value[index] = item + ''
  // })
  return item
}

const getTableData = async () => {
  const table = await getProductsList({
    page: page.value,
    pageSize: pageSize.value,
    ...searchInfo.value,
  })
  if (table.code === 0) {
    const resDataList = table.data.list || []
    const newList = []
    resDataList.forEach((item) => {
      item = execItem(item)
      newList.push(item)
    })
    tableData.value = newList
    total.value = table.data.total
  }
}

const deleteRow = (row) => {
  ElMessageBox.confirm('确定删除?').then(() => {
    deleteInfoFunc(row)
  })
}

const initShowDialogInfo = async (row) => {
  const res = await findProducts({ id: row.id })
  if (res.code === 0) {
    formData.value = res.data || {}
    formData.value.price = parseFloat(formData.value.price)

    // 将week_days数组中的数字转为字符串
    // if (formData.value.week_days[0] === 0) {
    //   weekList.forEach((item, index) => {
    //     week.value[index] = item
    //   })
    // } else {
    //   formData.value.week_days.forEach((item, index) => {
    //     week.value[index] = item + ''
    //   })
    // }
    if (formData.value.shelves_start_time && formData.value.shelves_end_time) {
      formData.value.shelves_timeRange = [formData.value.shelves_start_time, formData.value.shelves_end_time]
    }
    initProductCategoriesOptions()
    dialogShow.value = true
  }
}

const viewFunc = async (row) => {
  dType.value = 'view'
  dTitle.value = '查看'
  initShowDialogInfo(row)
}
const copyStock = (num) => {
  copyStockNumber.value = +JSON.stringify(JSON.parse(num))
}

const updateFunc = async (row, name) => {
  if (name === 'store') {
    if ((!row.stock && row.stock !== 0) || !isNumber.test(row.stock)) {
      row.stock = +copyStockNumber.value
      ElMessage.error('请输入正整数')
      return
    }
    dType.value = 'update'
    const res = await findProducts({ id: row.id })
    if (res.code === 0) {
      formData.value = res.data || {}
      formData.value.stock = +row.stock
      formData.value.price = parseFloat(formData.value.price)
      const val = await updateProducts(formData.value)
      if (val.code === 0) {
        ElMessage.success(val.msg)
      }
    }
  } else {
    week.value = []
    dType.value = 'update'
    dTitle.value = '更新'
    initShowDialogInfo(row)
  }
}

const deleteInfoFunc = async (row) => {
  const res = await deleteProducts({ id: row.id })
  if (res.code === 0) {
    ElMessage({ type: 'success', message: '删除成功' })
    getTableData()
  }
}

const openDialog = () => {
  dType.value = 'create'
  dTitle.value = '创建'
  dialogShow.value = true
}

const closeDialog = () => {
  dialogShow.value = false
  formData.value = {
    retail_area_id: null,
    cat_type: 1,
    product_category_id: null,
    name: '',
    image_url: '',
    price: 10,
    unit: '份',
    stock: 100,
    sale: 0,
    buy_limit: 0,
    shelves_switch: 0,
    code: '',
    purchase_price: '',
    special_status: 1,
    special_price: 0,
    shelves_days: [],
    shelves_timeRange: [],
    shelves_start_time: '',
    shelves_end_time: '',
    // week_days: ['1'],
  }
}

const enterDialog = async () => {
  elFormRef.value?.validate(async (valid) => {
    if (!valid) return
    // if (!formData.value.shelves_start_time) {
    //   ElMessage.error('请完善时间')
    //   return
    // }

    // 对周几参数进行赋值
    week.value.forEach((item, index) => {
      week.value[index] = +item
    })
    if (week.value.length === 5) {
      week.value = [0]
    }
    formData.value.week_days = JSON.parse(JSON.stringify(week.value))
    formData.value.shelves_start_time = formData.value.shelves_timeRange[0]
    formData.value.shelves_end_time = formData.value.shelves_timeRange[1]

    let res
    switch (dType.value) {
      case 'create':
        res = await createProducts(formData.value)
        break
      case 'update':
        res = await updateProducts(formData.value)
        break
      default:
        res = await createProducts(formData.value)
        break
    }
    if (res.code === 0) {
      ElMessage({ type: 'success', message: '创建/更改成功' })
      closeDialog()
      getTableData()
    }
  })
}

const initProductCategoriesOptions = () => {
  const api_req = {
    area_id: formData.value.retail_area_id,
    cat_type: formData.value.cat_type,
  }
  getProductCategoriesAll(api_req).then((res) => {
    if (res.code === 0) {
      categoriesOptions.value = res.data || []
    }
  })
}

const initSearProductCategoriesOptions = () => {
  const api_req = {
    area_id: dialogShowSet.value ? formDataSet.value.retail_area_id : searchInfo.value.retail_area_id,
    cat_type: dialogShowSet.value ? formDataSet.value.cat_type : searchInfo.value.cat_type,
  }
  getProductCategoriesAll(api_req).then((res) => {
    if (res.code === 0) {
      categoriesOptions.value = res.data || []
    }
  })
}

const initAllProductCategories = () => {
  productCategoriesAll().then((res) => {
    if (res.code === 0) {
      allCategoriesOptions.value = res.data || []
      const oMap = {}
      allCategoriesOptions.value.forEach((item) => {
        oMap[item.id] = item.name
      })

      allCategoriesMap.value = oMap
      getTableData()
    }
  })
}

onBeforeMount(() => {
  if (localStorage.getItem('products_columnSet')) {
    columnsList.value = JSON.parse(localStorage.getItem('products_columnSet'))
  }

  initAllProductCategories()
})
</script>

<style lang="scss" scoped>
.storeClass {
  width: 100%;

  :deep(.el-input__inner) {
    text-align: center;
  }

  :deep(.el-input__wrapper) {
    padding-left: 0;
    padding-right: 0;
  }

  :deep(.el-input-number__decrease) {
    display: none;
  }

  :deep(.el-input-number__increase) {
    display: none;
  }
}
</style>
<style>
.data-rang-class .el-picker-panel__footer {
  display: none;
}
</style>
