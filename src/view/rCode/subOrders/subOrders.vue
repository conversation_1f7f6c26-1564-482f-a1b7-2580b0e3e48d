<template>
  <div>
    <div class="gva-search-box">
      <el-form ref="elSearchFormRef" :inline="true" :model="searchInfo" class="demo-form-inline" :rules="searchRule" @keyup.enter="onSubmit">
      <el-form-item label="创建日期" prop="createdAt">
      <template #label>
        <span>
          创建日期
          <el-tooltip content="搜索范围是开始日期（包含）至结束日期（不包含）">
            <el-icon><QuestionFilled /></el-icon>
          </el-tooltip>
        </span>
      </template>
      <el-date-picker v-model="searchInfo.startCreatedAt" type="datetime" placeholder="开始日期" :disabled-date="time=> searchInfo.endCreatedAt ? time.getTime() > searchInfo.endCreatedAt.getTime() : false"></el-date-picker>
       —
      <el-date-picker v-model="searchInfo.endCreatedAt" type="datetime" placeholder="结束日期" :disabled-date="time=> searchInfo.startCreatedAt ? time.getTime() < searchInfo.startCreatedAt.getTime() : false"></el-date-picker>
      </el-form-item>
        <el-form-item label="orderId字段" prop="orderId">

             <el-input v-model.number="searchInfo.orderId" placeholder="搜索条件" />

        </el-form-item>
        <el-form-item label="userId字段" prop="userId">

        <el-input v-model.number="searchInfo.userId" placeholder="搜索条件" />

        </el-form-item>
        <el-form-item label="retailAreaId字段" prop="retailAreaId">

        <el-input v-model.number="searchInfo.retailAreaId" placeholder="搜索条件" />

        </el-form-item>
        <el-form-item label="productCategoryId字段" prop="productCategoryId">

        <el-input v-model.number="searchInfo.productCategoryId" placeholder="搜索条件" />

        </el-form-item>
        <el-form-item label="productId字段" prop="productId">

        <el-input v-model.number="searchInfo.productId" placeholder="搜索条件" />

        </el-form-item>
        <el-form-item label="amount字段" prop="amount">

        <el-input v-model.number="searchInfo.amount" placeholder="搜索条件" />

        </el-form-item>
        <el-form-item label="price字段" prop="price">
         <el-input v-model="searchInfo.price" placeholder="搜索条件" />

        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="search" @click="onSubmit">查询</el-button>
          <el-button icon="refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
        <div class="gva-btn-list">
            <el-button type="primary" icon="plus" @click="openDialog">新增</el-button>
            <el-popover v-model:visible="deleteVisible" :disabled="!multipleSelection.length" placement="top" width="160">
            <p>确定要删除吗？</p>
            <div style="text-align: right; margin-top: 8px;">
                <el-button type="primary" link @click="deleteVisible = false">取消</el-button>
                <el-button type="primary" @click="onDelete">确定</el-button>
            </div>
            <template #reference>
                <el-button icon="delete" style="margin-left: 10px;" :disabled="!multipleSelection.length" @click="deleteVisible = true">删除</el-button>
            </template>
            </el-popover>
        </div>
        <el-table
        ref="multipleTable"
        style="width: 100%"
        :max-height="680"
        tooltip-effect="dark"
        :data="tableData"
        row-key="ID"
        @selection-change="handleSelectionChange"
        @sort-change="sortChange"
        >
        <el-table-column type="selection" width="55" />
        <el-table-column align="left" label="日期" width="180">
            <template #default="scope">{{ formatDate(scope.row.CreatedAt) }}</template>
        </el-table-column>
        <el-table-column sortable align="left" label="orderId字段" prop="orderId" width="120" />
        <el-table-column sortable align="left" label="userId字段" prop="userId" width="120" />
        <el-table-column sortable align="left" label="retailAreaId字段" prop="retailAreaId" width="120" />
        <el-table-column sortable align="left" label="productCategoryId字段" prop="productCategoryId" width="120" />
        <el-table-column sortable align="left" label="productId字段" prop="productId" width="120" />
        <el-table-column sortable align="left" label="amount字段" prop="amount" width="120" />
        <el-table-column sortable align="left" label="price字段" prop="price" width="120" />
        <el-table-column align="left" label="操作" min-width="120">
            <template #default="scope">
            <el-button type="primary" link class="table-button" @click="getDetails(scope.row)">
                <el-icon style="margin-right: 5px"><InfoFilled /></el-icon>
                查看详情
            </el-button>
            <el-button type="primary" link icon="edit" class="table-button" @click="updateSubOrdersFunc(scope.row)">变更</el-button>
            <el-button type="primary" link icon="delete" @click="deleteRow(scope.row)">删除</el-button>
            </template>
        </el-table-column>
        </el-table>
        <div class="gva-pagination">
            <el-pagination
            layout="total, sizes, prev, pager, next, jumper"
            :current-page="page"
            :page-size="pageSize"
            :page-sizes="[10, 30, 50, 100]"
            :total="total"
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
            />
        </div>
    </div>
    <el-dialog v-model="dialogFormVisible" :before-close="closeDialog" :title="type==='create'?'添加':'修改'" destroy-on-close>
      <el-scrollbar height="500px">
          <el-form :model="formData" label-position="right" ref="elFormRef" :rules="rule" label-width="80px">
            <el-form-item label="orderId字段:"  prop="orderId" >
              <el-input v-model.number="formData.orderId" :clearable="true" placeholder="请输入orderId字段" />
            </el-form-item>
            <el-form-item label="userId字段:"  prop="userId" >
              <el-input v-model.number="formData.userId" :clearable="true" placeholder="请输入userId字段" />
            </el-form-item>
            <el-form-item label="retailAreaId字段:"  prop="retailAreaId" >
              <el-input v-model.number="formData.retailAreaId" :clearable="true" placeholder="请输入retailAreaId字段" />
            </el-form-item>
            <el-form-item label="productCategoryId字段:"  prop="productCategoryId" >
              <el-input v-model.number="formData.productCategoryId" :clearable="true" placeholder="请输入productCategoryId字段" />
            </el-form-item>
            <el-form-item label="productId字段:"  prop="productId" >
              <el-input v-model.number="formData.productId" :clearable="true" placeholder="请输入productId字段" />
            </el-form-item>
            <el-form-item label="amount字段:"  prop="amount" >
              <el-input v-model.number="formData.amount" :clearable="true" placeholder="请输入amount字段" />
            </el-form-item>
            <el-form-item label="price字段:"  prop="price" >
              <el-input v-model="formData.price" :clearable="true"  placeholder="请输入price字段" />
            </el-form-item>
          </el-form>
      </el-scrollbar>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeDialog">取 消</el-button>
          <el-button type="primary" @click="enterDialog">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog v-model="detailShow" style="width: 800px" lock-scroll :before-close="closeDetailShow" title="查看详情" destroy-on-close>
      <el-scrollbar height="550px">
        <el-descriptions column="1" border>
                <el-descriptions-item label="orderId字段">
                        {{ formData.orderId }}
                </el-descriptions-item>
                <el-descriptions-item label="userId字段">
                        {{ formData.userId }}
                </el-descriptions-item>
                <el-descriptions-item label="retailAreaId字段">
                        {{ formData.retailAreaId }}
                </el-descriptions-item>
                <el-descriptions-item label="productCategoryId字段">
                        {{ formData.productCategoryId }}
                </el-descriptions-item>
                <el-descriptions-item label="productId字段">
                        {{ formData.productId }}
                </el-descriptions-item>
                <el-descriptions-item label="amount字段">
                        {{ formData.amount }}
                </el-descriptions-item>
                <el-descriptions-item label="price字段">
                        {{ formData.price }}
                </el-descriptions-item>
        </el-descriptions>
      </el-scrollbar>
    </el-dialog>
  </div>
</template>

<script setup>
import {
  createSubOrders,
  deleteSubOrders,
  deleteSubOrdersByIds,
  findSubOrders,
  getSubOrdersList,
  updateSubOrders
} from '@/api/retail/subOrders'

// 全量引入格式化工具 请按需保留
import {formatDate} from '@/utils/format'
import {ElMessage, ElMessageBox} from 'element-plus'
import {reactive, ref} from 'vue'

defineOptions({
    name: 'SubOrders'
})

// 自动化生成的字典（可能为空）以及字段
const formData = ref({
        orderId: 0,
        userId: 0,
        retailAreaId: 0,
        productCategoryId: 0,
        productId: 0,
        amount: 0,
        price: '',
        })


// 验证规则
const rule = reactive({
               orderId : [{
                   required: true,
                   message: '',
                   trigger: ['input','blur'],
               },
              ],
               userId : [{
                   required: true,
                   message: '',
                   trigger: ['input','blur'],
               },
              ],
               retailAreaId : [{
                   required: true,
                   message: '',
                   trigger: ['input','blur'],
               },
              ],
               productCategoryId : [{
                   required: true,
                   message: '',
                   trigger: ['input','blur'],
               },
              ],
               productId : [{
                   required: true,
                   message: '',
                   trigger: ['input','blur'],
               },
              ],
               amount : [{
                   required: true,
                   message: '',
                   trigger: ['input','blur'],
               },
              ],
               price : [{
                   required: true,
                   message: '',
                   trigger: ['input','blur'],
               },
               {
                   whitespace: true,
                   message: '不能只输入空格',
                   trigger: ['input', 'blur'],
              }
              ],
})

const searchRule = reactive({
  createdAt: [
    { validator: (rule, value, callback) => {
      if (searchInfo.value.startCreatedAt && !searchInfo.value.endCreatedAt) {
        callback(new Error('请填写结束日期'))
      } else if (!searchInfo.value.startCreatedAt && searchInfo.value.endCreatedAt) {
        callback(new Error('请填写开始日期'))
      } else if (searchInfo.value.startCreatedAt && searchInfo.value.endCreatedAt && (searchInfo.value.startCreatedAt.getTime() === searchInfo.value.endCreatedAt.getTime() || searchInfo.value.startCreatedAt.getTime() > searchInfo.value.endCreatedAt.getTime())) {
        callback(new Error('开始日期应当早于结束日期'))
      } else {
        callback()
      }
    }, trigger: 'change' }
  ],
})

const elFormRef = ref()
const elSearchFormRef = ref()

// =========== 表格控制部分 ===========
const page = ref(1)
const total = ref(0)
const pageSize = ref(10)
const tableData = ref([])
const searchInfo = ref({})
// 排序
const sortChange = ({ prop, order }) => {
  searchInfo.value.sort = prop
  searchInfo.value.order = order
  getTableData()
}

// 重置
const onReset = () => {
  searchInfo.value = {}
  getTableData()
}

// 搜索
const onSubmit = () => {
  elSearchFormRef.value?.validate(async(valid) => {
    if (!valid) return
    page.value = 1
    pageSize.value = 10
    getTableData()
  })
}

// 分页
const handleSizeChange = (val) => {
  pageSize.value = val
  getTableData()
}

// 修改页面容量
const handleCurrentChange = (val) => {
  page.value = val
  getTableData()
}

// 查询
const getTableData = async() => {
  const table = await getSubOrdersList({ page: page.value, pageSize: pageSize.value, ...searchInfo.value })
  if (table.code === 0) {
    tableData.value = table.data.list
    total.value = table.data.total
    page.value = table.data.page
    pageSize.value = table.data.pageSize
  }
}

getTableData()

// ============== 表格控制部分结束 ===============

// 获取需要的字典 可能为空 按需保留
const setOptions = async () =>{
}

// 获取需要的字典 可能为空 按需保留
setOptions()


// 多选数据
const multipleSelection = ref([])
// 多选
const handleSelectionChange = (val) => {
    multipleSelection.value = val
}

// 删除行
const deleteRow = (row) => {
    ElMessageBox.confirm('确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
            deleteSubOrdersFunc(row)
        })
    }


// 批量删除控制标记
const deleteVisible = ref(false)

// 多选删除
const onDelete = async() => {
      const ids = []
      if (multipleSelection.value.length === 0) {
        ElMessage({
          type: 'warning',
          message: '请选择要删除的数据'
        })
        return
      }
      multipleSelection.value &&
        multipleSelection.value.map(item => {
          ids.push(item.ID)
        })
      const res = await deleteSubOrdersByIds({ ids })
      if (res.code === 0) {
        ElMessage({
          type: 'success',
          message: '删除成功'
        })
        if (tableData.value.length === ids.length && page.value > 1) {
          page.value--
        }
        deleteVisible.value = false
        getTableData()
      }
    }

// 行为控制标记（弹窗内部需要增还是改）
const type = ref('')

// 更新行
const updateSubOrdersFunc = async(row) => {
    const res = await findSubOrders({ ID: row.ID })
    type.value = 'update'
    if (res.code === 0) {
        formData.value = res.data.resubOrders
        dialogFormVisible.value = true
    }
}


// 删除行
const deleteSubOrdersFunc = async (row) => {
    const res = await deleteSubOrders({ ID: row.ID })
    if (res.code === 0) {
        ElMessage({
                type: 'success',
                message: '删除成功'
            })
            if (tableData.value.length === 1 && page.value > 1) {
            page.value--
        }
        getTableData()
    }
}

// 弹窗控制标记
const dialogFormVisible = ref(false)


// 查看详情控制标记
const detailShow = ref(false)


// 打开详情弹窗
const openDetailShow = () => {
  detailShow.value = true
}


// 打开详情
const getDetails = async (row) => {
  // 打开弹窗
  const res = await findSubOrders({ ID: row.ID })
  if (res.code === 0) {
    formData.value = res.data.resubOrders
    openDetailShow()
  }
}


// 关闭详情弹窗
const closeDetailShow = () => {
  detailShow.value = false
  formData.value = {
          orderId: 0,
          userId: 0,
          retailAreaId: 0,
          productCategoryId: 0,
          productId: 0,
          amount: 0,
          price: '',
          }
}


// 打开弹窗
const openDialog = () => {
    type.value = 'create'
    dialogFormVisible.value = true
}

// 关闭弹窗
const closeDialog = () => {
    dialogFormVisible.value = false
    formData.value = {
        orderId: 0,
        userId: 0,
        retailAreaId: 0,
        productCategoryId: 0,
        productId: 0,
        amount: 0,
        price: '',
        }
}
// 弹窗确定
const enterDialog = async () => {
     elFormRef.value?.validate( async (valid) => {
             if (!valid) return
              let res
              switch (type.value) {
                case 'create':
                  res = await createSubOrders(formData.value)
                  break
                case 'update':
                  res = await updateSubOrders(formData.value)
                  break
                default:
                  res = await createSubOrders(formData.value)
                  break
              }
              if (res.code === 0) {
                ElMessage({
                  type: 'success',
                  message: '创建/更改成功'
                })
                closeDialog()
                getTableData()
              }
      })
}

</script>

<style>

</style>
