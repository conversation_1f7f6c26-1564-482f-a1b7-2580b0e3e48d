<template>
  <div>
    <el-card shadow="hover">
      <el-form :inline="true" :model="searchInfo" @keyup.enter="onSubmit">
        <el-form-item label="车牌号" prop="bus_no">
          <el-input v-model="searchInfo.bus_no" clearable placeholder="请输入车牌号" />
        </el-form-item>
        <el-form-item label="联系人" prop="contacts">
          <el-input v-model="searchInfo.contacts" clearable placeholder="请输入联系人" />
        </el-form-item>
        <el-form-item label="联系电话" prop="telephone">
          <el-input v-model="searchInfo.telephone" clearable placeholder="请输入联系电话" />
        </el-form-item>
        <el-form-item label="班车状态" prop="status">
          <el-select v-model="searchInfo.status" clearable placeholder="请选择班车状态">
            <el-option v-for="item in statusList" :key="item" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="关联设备号" prop="gps_device_id">
          <el-select v-model="searchInfo.gps_device_id" clearable placeholder="请选择关联设备号" filterable>
            <el-option v-for="item in gps_device_idList" :key="item" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="search" @click="onSubmit">查询</el-button>
          <el-button icon="refresh" @click="onReset">重置</el-button>
          <el-button type="success" icon="plus" @click="openDialog">新增</el-button>
        </el-form-item>
      </el-form>
      <el-table ref="tableRef" :data="tableData" row-key="id" border @sort-change="sortChange" show-overflow-tooltip>
        <el-table-column align="center" label="ID" prop="id" :width="100" fixed="left" />
        <el-table-column align="center" label="创建时间" prop="created_at_str" :width="160" />
        <el-table-column align="center" label="车牌号" prop="bus_no" :min-width="120" />
        <el-table-column align="center" label="联系人" prop="contacts" :min-width="120" />
        <el-table-column align="center" label="联系电话" prop="telephone" :min-width="120" />
        <el-table-column align="center" label="班车状态" prop="status" :min-width="120">
          <template #default="scope">
            {{ statusMap[scope.row.status] }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="关联设备号" prop="gps_device_id" :min-width="120">
          <template #default="scope">
            {{ gps_device_idMap[scope.row.gps_device_id] }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="设备状态" prop="gps_status" :min-width="120">
          <template #default="scope">
            {{ statusMap[scope.row.gps_status] }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="操作" :width="280" fixed="right">
          <template #default="scope">
            <el-button type="primary" @click="viewFunc(scope.row)">查看详情</el-button>
            <el-button type="warning" @click="updateFunc(scope.row)">变更</el-button>
            <el-button type="danger" @click="deleteRow(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :layout="PageLayout"
        :current-page="page"
        :page-size="pageSize"
        :page-sizes="PageSizes"
        :total="total"
        @current-change="pageChange"
        @size-change="pageSizeChange"
      />
    </el-card>
    <el-dialog v-model="dialogShow" :before-close="closeDialog" :title="dTitle" top="10px" destroy-on-close>
      <el-form :model="formData" :disabled="dType === 'view'" label-position="right" ref="elFormRef" :rules="formDataRule" label-width="120px">
        <el-form-item label="车牌号" prop="bus_no">
          <el-input v-model="formData.bus_no" placeholder="请输入车牌号" :maxlength="8" show-word-limit clearable />
        </el-form-item>
        <el-form-item label="联系人" prop="contacts">
          <el-input v-model="formData.contacts" placeholder="请输入联系人" :maxlength="64" show-word-limit clearable />
        </el-form-item>
        <el-form-item label="联系电话" prop="telephone">
          <el-input v-model="formData.telephone" placeholder="请输入联系电话" :maxlength="11" show-word-limit clearable />
        </el-form-item>
        <el-form-item label="班车状态" prop="status">
          <el-select v-model="formData.status" placeholder="请选择班车状态" clearable style="width: 100%">
            <el-option v-for="item in statusList" :key="item" :label="item.label" :value="item.value" />
          </el-select>
          <!-- <el-input-number v-model="formData.status" style="width: 100%" /> -->
        </el-form-item>
        <el-form-item label="关联设备号" prop="gps_device_id">
          <el-select v-model="formData.gps_device_id" clearable placeholder="请选择" style="width: 100%" filterable>
            <el-option v-for="item in gps_device_idList1" :key="item" :label="item.label" :value="item.value" />
          </el-select>
          <!-- <el-input-number v-model="formData.gps_device_id" style="width: 100%" /> -->
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer" v-show="dType !== 'view'">
          <el-button @click="closeDialog">取 消</el-button>
          <el-button type="primary" @click="enterDialog">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { createBus, updateBus, deleteBus, deleteBusByIds, findBus, getBusList } from '@/api/retail/bus'
import { formatDate } from '@/utils/format'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ref, reactive, onBeforeMount } from 'vue'
import { PageLayout, PageSizes } from '@/utils/const'
import { useRouter, useRoute } from 'vue-router'
import { useUserStore } from '@/pinia/modules/user'
import { getGpsDeviceList } from '@/api/retail/gpsDevice'

const userStore = useUserStore()
const router = useRouter()
const route = useRoute()
const dialogShow = ref(false)
const dTitle = ref('')
const dType = ref('')
const elFormRef = ref()
const page = ref(1)
const total = ref(0)
const pageSize = ref(100)
const tableData = ref([])
const searchInfo = ref({})
const formData = ref({ bus_no: '', contacts: '', telephone: '', status: 1, gps_device_id: '' })
const formDataRule = reactive({
  bus_no: [{ required: true, message: '请输入车牌号', trigger: ['blur,change'] }],
  contacts: [{ required: true, message: '请输入联系人', trigger: ['blur,change'] }],
  status: [{ required: true, message: '请选择班车状态', trigger: ['blur,change'] }],
  gps_device_id: [{ required: true, message: '请选择关联设备号', trigger: ['blur,change'] }],
  telephone: [
    {
      required: true,
      message: '请输入联系电话',
      trigger: ['blur,change'],
    },
    {
      pattern: /^1[3-9]\d{9}$/,
      message: '请输入正确的手机号',
      trigger: ['blur,change'],
    },
  ],
})

const gps_device_idList = ref([])
const gps_device_idList1 = ref([])
const gps_device_idMap = ref({})
const statusList = ref([
  {
    label: '正常',
    value: 1,
  },
  {
    label: '故障',
    value: 2,
  },
])
const statusMap = {
  1: '正常',
  2: '故障',
  3: '未连接'
}

const sortChange = ({ prop, order }) => {
  searchInfo.value.sort_prop = prop
  searchInfo.value.sort_desc = order !== 'ascending'
  getTableData()
}

const onReset = () => {
  searchInfo.value = {}
  getTableData()
}

const onSubmit = () => {
  page.value = 1
  getTableData()
}

const pageSizeChange = (val) => {
  pageSize.value = val
  getTableData()
}

const pageChange = (val) => {
  page.value = val
  getTableData()
}

const execItem = (item) => {
  item.created_at_str = formatDate(item.created_at)
  return item
}

const getTableData = async () => {
  const table = await getBusList({ page: page.value, page_size: pageSize.value, ...searchInfo.value })
  if (table.code === 0) {
    let resDataList = table.data.list || []
    let newList = []
    resDataList.forEach((item) => {
      item = execItem(item)
      newList.push(item)
    })
    tableData.value = newList
    total.value = table.data.total
  }
}

const deleteRow = (row) => {
  ElMessageBox.confirm('确定删除?').then(() => {
    deleteInfoFunc(row)
  })
}

const initShowDialogInfo = async (row) => {
  const res = await findBus({ id: row.id })
  if (res.code === 0) {
    formData.value = res.data || {}
    dialogShow.value = true
  }
}

const viewFunc = async (row) => {
  dType.value = 'view'
  dTitle.value = '查看'
  initShowDialogInfo(row)
  gps_device_idList1.value = gps_device_idList.value
}

const updateFunc = async (row) => {
  dType.value = 'update'
  dTitle.value = '更新'
  initShowDialogInfo(row)
  gps_device_idList1.value = gps_device_idList.value
}

const deleteInfoFunc = async (row) => {
  const res = await deleteBus({ id: row.id })
  if (res.code === 0) {
    ElMessage({ type: 'success', message: '删除成功' })
    getTableData()
  }
}

const openDialog = async() => {
  dType.value = 'create'
  dTitle.value = '创建'
  dialogShow.value = true
  await initGps_device_idList()
  gps_device_idList1.value = gps_device_idList.value.filter((item) => item.is_bind !== 1) // 过滤掉不符合条件的项
}

const closeDialog = () => {
  dialogShow.value = false
  formData.value = { bus_no: '', contacts: '', telephone: '', status: 1, gps_device_id: '' }
}

const enterDialog = async () => {
  elFormRef.value?.validate(async (valid) => {
    if (!valid) return
    let res
    switch (dType.value) {
      case 'create':
        res = await createBus(formData.value)
        break
      case 'update':
        res = await updateBus(formData.value)
        break
      default:
        res = await createBus(formData.value)
        break
    }
    if (res.code === 0) {
      ElMessage({ type: 'success', message: '创建/更改成功' })
      closeDialog()
      getTableData()
    }
  })
}
const initGps_device_idList = () => {
  getGpsDeviceList({ page: 1, page_size: 2000 }).then((res) => {
    if (res.code === 0) {
      // 只返回res.data里面的id 和 name
      gps_device_idList.value = res.data.list
        // .filter(item => item.is_bind !== 1) // 过滤掉不符合条件的项
        .map((item) => {
          return { label: item.device_no, value: Number(item.id), is_bind: item.is_bind }
        })
      res.data.list.forEach((item) => {
        gps_device_idMap.value[item.id] = item.device_no
      })
    }
  })
}
onBeforeMount(async () => {
  await initGps_device_idList()
  getTableData()
})
</script>

<style></style>
