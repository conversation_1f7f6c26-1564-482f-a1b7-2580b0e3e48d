<template>
  <div>
    <el-card shadow="hover">
      <el-form :inline="true" :model="searchInfo" @keyup.enter="onSubmit">
        <el-form-item label="标题" prop="title">
          <el-input v-model="searchInfo.title" clearable placeholder="请输入标题" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="search" @click="onSubmit">查询</el-button>
          <el-button icon="refresh" @click="onReset">重置</el-button>
          <el-button type="success" icon="plus" @click="openDialog">新增</el-button>
        </el-form-item>
      </el-form>

      <el-table ref="tableRef" :data="tableData" row-key="id" border @sort-change="sortChange" show-overflow-tooltip>
        <el-table-column align="center" label="ID" prop="id" :width="100"/>
        <el-table-column align="center" label="创建时间" prop="created_at_str" :width="160" />
        <el-table-column align="center" label="标题" prop="title" :min-width="120" />
        <el-table-column align="center" label="预览图地址" prop="cover_url" :width="120">
          <template #default="scope">
            <el-image :preview-src-list="[scope.row.cover_url_full]" :src="scope.row.cover_url_full" fit="contain" style="width: 80%;"> </el-image>
          </template>
        </el-table-column>
        <el-table-column align="center" label="内容地址" :width="120">
          <template #default="scope">
            <el-button type="primary" link @click="openContentUrl(scope.row)">打开</el-button>
          </template>
        </el-table-column>
        <!-- <el-table-column align="center" label="排序" prop="sort" :width="120" /> -->
        <el-table-column align="center" label="是否显示" prop="status" :width="120">
          <template #default="scope">
            <el-switch disabled v-model="scope.row.status" :active-value="1" :inactive-value="2" />
          </template>
        </el-table-column>
        <el-table-column align="center" label="操作" :width="280">
          <template #default="scope">
            <el-button type="primary" @click="viewFunc(scope.row)">查看详情</el-button>
            <el-button type="warning" @click="updateFunc(scope.row)">变更</el-button>
            <el-button type="danger" @click="deleteRow(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :layout="PageLayout"
        :current-page="page"
        :page-size="pageSize"
        :page-sizes="PageSizes"
        :total="total" 
        @current-change="pageChange"
        @size-change="pageSizeChange"
      />
    </el-card>
    <el-dialog v-model="dialogShow" :before-close="closeDialog" :title="dTitle" top="10px" class="w-[90%] md:w-[50%]" destroy-on-close>
      <el-form :model="formData" :disabled="dType === 'view'" label-position="left" ref="elFormRef" :rules="formDataRule" label-width="120px">
        <el-form-item class="block md:flex" label="标题" prop="title">
          <el-input v-model="formData.title" placeholder="请输入标题" :maxlength="128" show-word-limit clearable />
        </el-form-item>
        <el-form-item  class="block md:flex" label="预览图地址" prop="cover_url">
          <UploadImg :img-url="formData.cover_url" :file-remove="previewImgFileRemove" :upload-success="previewImgFileUploadSuccess" :file-type="FileTypeNotifyMsgPreviewImg" />
        </el-form-item>
        <el-form-item class="block md:flex"  label="内容地址" prop="content_url">
          <UploadFile :file-url="formData.content_url" :file-remove="contentUrlFileRemove" :upload-success="contentUrlFileUploadSuccess" :file-type="FileTypeNotifyMsgContent" />
        </el-form-item>
        <!-- <el-form-item label="排序" prop="sort">
            <el-input-number v-model="formData.sort" :precision="0" :min="0" />
          </el-form-item> -->

        <el-form-item class="block md:flex"  label="是否显示" prop="status">
          <el-switch v-model="formData.status" :active-value="1" :inactive-value="2" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer" v-show="dType !== 'view'">
          <el-button @click="closeDialog">取 消</el-button>
          <el-button type="primary" @click="enterDialog">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { createBusNews, updateBusNews, findBusNews, deleteBusNews, getBusNewsList } from '@/api/retail/busDynamics'
import { formatDate, getOssFileUrl } from '@/utils/format'
import { ElMessage, ElMessageBox } from 'element-plus'
import { onBeforeMount, onBeforeUnmount, reactive, shallowRef, watch, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useUserStore } from '@/pinia/modules/user'
import UploadImg from '@/components/upload/uploadImg.vue'
import UploadFile from '@/components/upload/uploadFile.vue'

const userStore = useUserStore()
const router = useRouter()
const route = useRoute()
const dialogShow = ref(false)
const dTitle = ref('')
const dType = ref('')
const elFormRef = ref()
const page = ref(1)
const total = ref(0)
const pageSize = ref(100)
const tableData = ref([])
const searchInfo = ref({})
const formData = ref({ title: '', cover_url: '', content_url: '', status: 1 })
const formDataRule = reactive({
  message_type: [{ required: true, message: '请完善该值', trigger: ['input', 'blur', 'change'] }],
  title: [
    { required: true, message: '请完善该值', trigger: ['input', 'blur'] },
    { whitespace: true, message: '不能只输入空格', trigger: ['input', 'blur'] },
  ],
  cover_url: [
    { required: true, message: '请完善该值', trigger: ['input', 'blur'] },
    { whitespace: true, message: '不能只输入空格', trigger: ['input', 'blur'] },
  ],
  content_url: [
    { required: true, message: '请完善该值', trigger: ['input', 'blur'] },
    { whitespace: true, message: '不能只输入空格', trigger: ['input', 'blur'] },
  ],
})

const openContentUrl = (row) => {
  window.open(row.content_url_full, '_blank')
}
const previewImgFileUploadSuccess = (response, uploadFile) => {
  if (response.code === 0) {
    formData.value.cover_url = response.data.key
  }
}

const previewImgFileRemove = (uploadFile) => {
  formData.value.cover_url = null
}

const contentUrlFileUploadSuccess = (response, uploadFile) => {
  if (response.code === 0) {
    formData.value.content_url = response.data.key
  }
}

const contentUrlFileRemove = (uploadFile) => {
  formData.value.content_url = null
}

const sortChange = ({ prop, order }) => {
  searchInfo.value.sort_prop = prop
  searchInfo.value.sort_desc = order !== 'ascending'
  getTableData()
}

const onReset = () => {
  searchInfo.value = {}
  getTableData()
}

const onSubmit = () => {
  page.value = 1
  getTableData()
}

const pageSizeChange = (val) => {
  pageSize.value = val
  getTableData()
}

const pageChange = (val) => {
  page.value = val
  getTableData()
}

const execItem = (item) => {
  item.created_at_str = formatDate(item.created_at)
  item.cover_url_full = getOssFileUrl(item.cover_url)
  item.content_url_full = getOssFileUrl(item.content_url)
  return item
}

const getTableData = async () => {
  const table = await getBusNewsList({ page: page.value, page_size: pageSize.value, ...searchInfo.value })
  if (table.code === 0) {
    let resDataList = table.data.list || []
    let newList = []
    resDataList.forEach((item) => {
      item = execItem(item)
      newList.push(item)
    })
    tableData.value = newList
    total.value = table.data.total
  }
}

const deleteRow = (row) => {
  ElMessageBox.confirm('确定删除?').then(() => {
    deleteInfoFunc(row)
  })
}

const initShowDialogInfo = async (row) => {
  const res = await findBusNews({ id: row.id })
  if (res.code === 0) {
    formData.value = res.data || {}
    dialogShow.value = true
  }
}

const viewFunc = async (row) => {
  dType.value = 'view'
  dTitle.value = '查看'
  initShowDialogInfo(row)
}

const updateFunc = async (row) => {
  dType.value = 'update'
  dTitle.value = '更新'
  initShowDialogInfo(row)
}

const deleteInfoFunc = async (row) => {
  const res = await deleteBusNews({ id: row.id })
  if (res.code === 0) {
    ElMessage({ type: 'success', message: '删除成功' })
    getTableData()
  }
}

const openDialog = () => {
  dType.value = 'create'
  dTitle.value = '创建'
  dialogShow.value = true
}

const closeDialog = () => {
  dialogShow.value = false
  formData.value = { title: '', cover_url: '', content_url: '', status: 1 }
}

const enterDialog = async () => {
  elFormRef.value?.validate(async (valid) => {
    if (!valid) return
    let res
    switch (dType.value) {
      case 'create':
        res = await createBusNews(formData.value)
        break
      case 'update':
        res = await updateBusNews(formData.value)
        break
      default:
        res = await createBusNews(formData.value)
        break
    }
    if (res.code === 0) {
      ElMessage({ type: 'success', message: '创建/更改成功' })
      closeDialog()
      getTableData()
    }
  })
}

onBeforeMount(() => {
  getTableData()
})
</script>

<style lang="scss" scoped>

@media screen and (max-width: 768px) {
  :deep(.el-upload-list__item-file-name){
  text-align: left;
  word-break: break-all !important;
  white-space: normal !important;
}

}

</style>
