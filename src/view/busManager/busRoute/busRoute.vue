<template>
  <div>
    <el-card shadow="hover">
      <el-form :inline="true" :model="searchInfo" @keyup.enter="onSubmit">
        <el-form-item label="路线类型" prop="route_type">
          <el-select v-model="searchInfo.route_type" clearable placeholder="请选择路线类型">
            <el-option v-for="item in routeList" :key="item" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="路线名称" prop="route_name">
          <el-input v-model="searchInfo.route_name" clearable placeholder="请输入路线名称" />
        </el-form-item>
        <el-form-item label="所属类型" prop="area_id">
          <el-select v-model="searchInfo.area_id" clearable placeholder="请选择所属类型">
            <el-option v-for="item in areaList" :key="item" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="班车ID" prop="bus_id">
          <el-select v-model="searchInfo.bus_id" clearable placeholder="请选择班车ID">
            <el-option v-for="item in busList" :key="item" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="search" @click="onSubmit">查询</el-button>
          <el-button icon="refresh" @click="onReset">重置</el-button>
          <el-button type="success" icon="plus" @click="openDialog">新增</el-button>
        </el-form-item>
      </el-form>
      <el-table ref="tableRef" :data="tableData" row-key="id" border @sort-change="sortChange" show-overflow-tooltip>
        <el-table-column align="center" label="ID" prop="id" :width="100" fixed="left" />
        <el-table-column align="center" label="创建时间" prop="created_at_str" :width="160" />
        <el-table-column align="center" label="路线类型" prop="route_type" :min-width="120">
          <template #default="scope">
            {{ routeMap[scope.row.route_type] }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="路线名称" prop="route_name" :min-width="120" />
        <el-table-column align="center" label="所属类型" prop="area_id" :min-width="120">
          <template #default="scope">
            {{ areaMap[scope.row.area_id] }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="班车ID" prop="bus_id" :min-width="120">
          <template #default="scope">
            {{ busMap[scope.row.bus_id] }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="班车司机" prop="contacts" :min-width="120" />
        <el-table-column align="center" label="联系电话" prop="telephone" :min-width="120" />
        <el-table-column align="center" label="操作" :width="280" fixed="right">
          <template #default="scope">
            <el-button type="primary" @click="viewFunc(scope.row)">绑定站点</el-button>
            <el-button type="warning" @click="updateFunc(scope.row)">变更</el-button>
            <el-button type="danger" @click="deleteRow(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :layout="PageLayout"
        :current-page="page"
        :page-size="pageSize"
        :page-sizes="PageSizes"
        :total="total"
        @current-change="pageChange"
        @size-change="pageSizeChange"
      />
    </el-card>
    <el-dialog v-model="dialogShow" :before-close="closeDialog" :title="dTitle" top="10px" destroy-on-close>
      <el-form :model="formData" :disabled="dType === 'view'" label-position="right" ref="elFormRef" :rules="formDataRule" label-width="80px">
        <el-form-item label="路线类型" prop="route_type">
          <el-select v-model="formData.route_type" style="width: 100%" clearable placeholder="请选择路线类型">
            <el-option v-for="item in routeList" :key="item" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="路线名称" prop="route_name">
          <el-input v-model="formData.route_name" placeholder="请选择路线名称" :maxlength="64" show-word-limit clearable />
        </el-form-item>
        <el-form-item label="所属类型" prop="area_id">
          <el-select v-model="formData.area_id" clearable style="width: 100%" placeholder="请选择所属类型">
            <el-option v-for="item in areaList" :key="item" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="班车ID" prop="bus_id">
          <el-select v-model="formData.bus_id" clearable filterable style="width: 100%" placeholder="请选择班车ID">
            <el-option v-for="item in busList" :key="item" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer" v-show="dType !== 'view'">
          <el-button @click="closeDialog">取 消</el-button>
          <el-button type="primary" @click="enterDialog">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog v-model="siteShow" :before-close="closeSiteDialog" :title="dTitle" width="70%" top="10px" destroy-on-close>
      <div class="flex justify-center items-center">
        <el-transfer
          v-model="rightValue"
          filterable
          filter-placeholder="请输入站点名称"
          :titles="['所有站点', '当前线路站点']"
          :format="{
            noChecked: '${total}',
            hasChecked: '${checked}/${total}',
          }"
          :data="siteData"
          @change="handleChange"
          target-order="push"
        >
          <template #default="{ option }">
            <div class="flex justify-between items-center">
              <span class="flex-1 truncate"> {{ option.label }}</span>
              <div class="w-[140px] time mr-6">
                <el-time-select v-model="option.time" value-format="HH:mm:ss" format="HH:mm:ss" start="06:00" end="23:59" step="00:05" />
              </div>
              <el-icon class="time handle"><Rank /></el-icon>
            </div>
          </template>
        </el-transfer>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeSiteDialog">取 消</el-button>
          <el-button type="primary" @click="enterSiteDialog">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { createBusRoute, updateBusRoute, deleteBusRoute, deleteBusRouteByIds, findBusRoute, getBusRouteList, getArea, getRouteType } from '@/api/retail/busRoute'
import { formatDate } from '@/utils/format'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ref, reactive, onBeforeMount, nextTick } from 'vue'
import { PageLayout, PageSizes } from '@/utils/const'
import { useRouter, useRoute } from 'vue-router'
import { useUserStore } from '@/pinia/modules/user'
import { createBus, updateBus, deleteBus, deleteBusByIds, findBus, getBusList } from '@/api/retail/bus'
import Sortable from 'sortablejs'
import { createBusSite, updateBusSite, deleteBusSite, deleteBusSiteByIds, findBusSite, getBusSiteList } from '@/api/retail/busSite'
import { createBusRouteSiteRelation, getBusRouteSiteRelationList } from '@/api/retail/busRouteSiteRelation'
const userStore = useUserStore()
const router = useRouter()
const route = useRoute()
const dialogShow = ref(false)
const siteShow = ref(false)
const dTitle = ref('')
const dType = ref('')
const elFormRef = ref()
const page = ref(1)
const total = ref(0)
const pageSize = ref(100)
const tableData = ref([])
const searchInfo = ref({})
const formData = ref({ route_type: '', route_name: '', area_id: '', bus_id: '' })
const formDataRule = reactive({
  route_type: [{ required: true, message: '请选择路线类型', trigger: 'change' }],
  route_name: [{ required: true, message: '请输入路线名称', trigger: 'blur' }],
  area_id: [{ required: true, message: '请选择所属类型', trigger: 'change' }],
  bus_id: [{ required: true, message: '请选择班车', trigger: 'change' }],
})
const siteData = ref([])
const siteDataOrigin = ref([])
const rightValue = ref([])
const currentRoute = ref({})
const handleChange = (value, direction, movedKeys) => {
  console.log(value, direction, movedKeys)
}
const sortChange = ({ prop, order }) => {
  searchInfo.value.sort_prop = prop
  searchInfo.value.sort_desc = order !== 'ascending'
  getTableData()
}

const onReset = () => {
  searchInfo.value = {}
  getTableData()
}

const onSubmit = () => {
  page.value = 1
  getTableData()
}

const pageSizeChange = (val) => {
  pageSize.value = val
  getTableData()
}

const pageChange = (val) => {
  page.value = val
  getTableData()
}

const execItem = (item) => {
  item.created_at_str = formatDate(item.created_at)
  return item
}

const getTableData = async () => {
  const table = await getBusRouteList({ page: page.value, page_size: pageSize.value, ...searchInfo.value })
  if (table.code === 0) {
    let resDataList = table.data.list || []
    let newList = []
    resDataList.forEach((item) => {
      item = execItem(item)
      newList.push(item)
    })
    tableData.value = newList
    total.value = table.data.total
  }
}

const deleteRow = (row) => {
  ElMessageBox.confirm('确定删除?').then(() => {
    deleteInfoFunc(row)
  })
}

const initShowDialogInfo = async (row) => {
  const res = await findBusRoute({ id: row.id })
  if (res.code === 0) {
    formData.value = res.data || {}
    dialogShow.value = true
  }
}
const queryBindSite = async () => {
  const res = await getBusRouteSiteRelationList({ bus_route_id: currentRoute.value.id, sort_prop: 'route_sort' })
  if (res.code === 0) {
    rightValue.value = res.data.list.map((item) => item.bus_site_id)

    siteData.value = JSON.parse(JSON.stringify(siteDataOrigin.value))
    siteData.value.forEach((item) => {
      res.data.list.forEach((items) => {
        if (item.key == items.bus_site_id) {
          item.time = items.time_column
        }
      })
    })
  }
}
const viewFunc = async (row) => {
  dType.value = 'view'
  dTitle.value = '绑定站点'
  siteShow.value = true
  currentRoute.value = row
  await querySite()

  queryBindSite()
  nextTick(() => {
    const elements = document.getElementsByClassName('el-transfer-panel__list')
    // console.log(elements)
    const el = elements[1] // 选择第一个匹配的元素

    Sortable.create(el, {
      animation: 150, // 拖拽动画
      handle: '.handle', // 指定可以拖拽的手柄
      ghostClass: 'ghost', // 拖拽时的样式
      onEnd: (event) => {
        // console.log(event)
        const { oldIndex, newIndex } = event
        const movedItem = rightValue.value.splice(oldIndex, 1)[0]
        rightValue.value.splice(newIndex, 0, movedItem)
        // console.log('拖拽完成后的顺序：', rightValue.value)
      },
    })
  })
}

const updateFunc = async (row) => {
  dType.value = 'update'
  dTitle.value = '更新'
  initShowDialogInfo(row)
}

const deleteInfoFunc = async (row) => {
  const res = await deleteBusRoute({ id: row.id })
  if (res.code === 0) {
    ElMessage({ type: 'success', message: '删除成功' })
    getTableData()
  }
}

const openDialog = () => {
  dType.value = 'create'
  dTitle.value = '创建'
  dialogShow.value = true
}

const closeDialog = () => {
  dialogShow.value = false
  formData.value = { route_type: '', route_name: '', area_id: '', bus_id: '' }
}
const closeSiteDialog = () => {
  siteShow.value = false
  rightValue.value = []
}

const enterSiteDialog = async () => {
  console.log(rightValue.value)
  console.log(siteData.value)
  if (!rightValue.value.length) {
    ElMessage({ type: 'error', message: '请先选择站点' })
    return
  }
  let targetParam = []
  try {
    rightValue.value.forEach((item, index) => {
      siteData.value.forEach((items) => {
        if (item == items.key) {
          if (!items.time) {
            ElMessage({ type: 'error', message: `请选择${items.label}的站点时间` })
            throw new Error('请选择站点时间')
          }
          targetParam.push({
            bus_route_id: currentRoute.value.id,
            bus_site_id: item,
            is_start_site: index == 0 ? 1 : 2,
            is_en_site: index == rightValue.value.length - 1 ? 1 : 2,
            route_sort: index + 1,
            time_column: items.time,
            latitude: items.latitude,
            longitude: items.longitude,
          })
        }
      })
    })
  } catch (error) {
    return
  }

  let param = {
    bus_route_id: currentRoute.value.id,
    all_data: targetParam,
  }

  console.log(param)

  const res = await createBusRouteSiteRelation(param)
  if (res.code === 0) {
    ElMessage({ type: 'success', message: '创建/更改成功' })
    closeSiteDialog()
  }
  console.log(param)
}

const enterDialog = async () => {
  elFormRef.value?.validate(async (valid) => {
    if (!valid) return
    let res
    switch (dType.value) {
      case 'create':
        res = await createBusRoute(formData.value)
        break
      case 'update':
        res = await updateBusRoute(formData.value)
        break
      default:
        res = await createBusRoute(formData.value)
        break
    }
    if (res.code === 0) {
      ElMessage({ type: 'success', message: '创建/更改成功' })
      closeDialog()
      getTableData()
    }
  })
}

const routeList = ref([])
const routeMap = ref({})
const areaList = ref([])
const areaMap = ref({})
const busList = ref([])
const busMap = ref({})
const initRouteTypeList = () => {
  getRouteType({}).then((res) => {
    if (res.code === 0) {
      // 只返回res.data里面的id 和 name
      routeList.value = res.data.list.map((item) => {
        return { label: item.type_name, value: Number(item.id) }
      })
      // 处理成 map 格式为 { id: name,id:name }
      res.data.list.forEach((item) => {
        routeMap.value[item.id] = item.type_name
      })
    }
  })
}
const initAreaList = () => {
  getArea({}).then((res) => {
    if (res.code === 0) {
      // 只返回res.data里面的id 和 name
      areaList.value = res.data.list.map((item) => {
        return { label: item.area_name, value: Number(item.id) }
      })
      // 处理成 map 格式为 { id: name,id:name }
      res.data.list.forEach((item) => {
        areaMap.value[item.id] = item.area_name
      })
    }
  })
}

const initBusList = () => {
  getBusList({ page: 1, page_size: 2000 }).then((res) => {
    if (res.code === 0) {
      // 只返回res.data里面的id 和 name
      busList.value = res.data.list.map((item) => {
        return { label: `${item.bus_no}(${item.contacts})`, value: Number(item.id) }
      })
      // 处理成 map 格式为 { id: name,id:name }
      res.data.list.forEach((item) => {
        busMap.value[item.id] = item.bus_no
      })
    }
  })
}

const querySite = async () => {
  let siteType = ''
  //如果是上班
  if (currentRoute.value.route_type == 1) {
    siteType = 1
  } else if (currentRoute.value.route_type == 2) {
    siteType = 2
  }
  const res = await getBusSiteList({ page: 1, site_type: siteType, page_size: 2000 })
  if (res.code === 0) {
    siteData.value = res.data.list.map((item) => {
      let label = `${item.site_name}`
      if (!siteType) {
        label = `${item.site_name}(${item.site_type == 1 ? '启程' : '归巢'})`
      }
      return { label: label, key: item.id, time: '', longitude: item.longitude, latitude: item.latitude }
    })
    // rightValue.value = res.data.map(item => item.id)
    siteDataOrigin.value = JSON.parse(JSON.stringify(siteData.value))
    console.log(siteData.value)
  }
}

onBeforeMount(async () => {
  await initRouteTypeList()
  await initAreaList()
  await initBusList()
  getTableData()
})
</script>

<style lang="scss" scoped>
:deep(.el-transfer-panel) {
  width: 25vw;
  height: 70vh;
}

:deep(.el-transfer-panel:first-of-type) .time {
  display: none;
}

:deep(.el-transfer-panel__item):last-of-type {
  margin-right: 30px;
}
:deep(.el-transfer-panel__item) {
  --el-transfer-item-height: 40px;
  .el-checkbox__input {
    top: 13px;
  }
}
:deep(.el-transfer-panel__body) {
  height: 90%;
}
</style>
