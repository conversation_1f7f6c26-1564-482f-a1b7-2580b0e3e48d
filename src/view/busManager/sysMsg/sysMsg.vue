<template>
  <div>
    <el-card shadow="hover">
      <el-form :inline="true" :model="searchInfo" @keyup.enter="onSubmit">
        <el-form-item label="标题" prop="title">
          <el-input v-model="searchInfo.title" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="通知内容" prop="content">
          <el-input v-model="searchInfo.content" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="search" @click="onSubmit">查询</el-button>
          <el-button icon="refresh" @click="onReset">重置</el-button>
          <el-button type="success" icon="plus" @click="openDialog">新增</el-button>
        </el-form-item>
      </el-form>
      <el-table ref="tableRef" :data="tableData" row-key="id" border @sort-change="sortChange" show-overflow-tooltip>
        <el-table-column align="center" label="ID" prop="id" :width="100" fixed="left" />
        <el-table-column align="center" label="创建时间" prop="created_at_str" :width="160" />
        <el-table-column align="center" label="标题" prop="title" :min-width="120" />
        <el-table-column align="center" label="通知内容" prop="content" :min-width="120" />
        <el-table-column align="center" label="操作" :width="280" >
          <template #default="scope">
            <el-button type="primary" @click="viewFunc(scope.row)">查看详情</el-button>
            <el-button type="warning" @click="updateFunc(scope.row)">变更</el-button>
            <el-button type="danger" @click="deleteRow(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination :layout="PageLayout" :current-page="page" :page-size="pageSize" :page-sizes="PageSizes"
        :total="total" @current-change="pageChange" @size-change="pageSizeChange" />
    </el-card>
    <el-dialog v-model="dialogShow" :before-close="closeDialog" :title="dTitle" class="w-[90%] md:w-[50%]" destroy-on-close>
      <el-form :model="formData" :disabled="dType === 'view'" label-position="left" ref="elFormRef"
        :rules="formDataRule" label-width="80px">
        <el-form-item label="标题" class="block md:flex" prop="title">
          <el-input v-model="formData.title" :maxlength="10" show-word-limit clearable />
        </el-form-item>
        <el-form-item label="通知内容" class="block md:flex" prop="content">
          <el-input v-model="formData.content" type="textarea" :autosize="{ minRows: 5, maxRows: 10 }" :maxlength="64"
            show-word-limit clearable />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer" v-show="dType !== 'view'">
          <el-button @click="closeDialog">取 消</el-button>
          <el-button type="primary" @click="enterDialog">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import {
  createSysMsg,
  updateSysMsg,
  deleteSysMsg,
  deleteSysMsgByIds,
  findSysMsg,
  getSysMsgList
} from '@/api/retail/sysMsg'
import { formatDate } from '@/utils/format'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ref, reactive, onBeforeMount } from 'vue'
import { PageLayout, PageSizes } from "@/utils/const"
import { useRouter, useRoute } from 'vue-router'
import { useUserStore } from "@/pinia/modules/user";


const userStore = useUserStore()
const router = useRouter()
const route = useRoute()
const dialogShow = ref(false)
const dTitle = ref('')
const dType = ref('')
const elFormRef = ref()
const page = ref(1)
const total = ref(0)
const pageSize = ref(100)
const tableData = ref([])
const searchInfo = ref({})
const formData = ref({ title: '', content: '', })
const formDataRule = reactive({
  title: [
    { required: true, message: '请完善该值', trigger: ['input', 'blur'] },
    { whitespace: true, message: '不能只输入空格', trigger: ['input', 'blur'] },
  ],
  content: [
    { required: true, message: '请完善该值', trigger: ['input', 'blur'] },
    { whitespace: true, message: '不能只输入空格', trigger: ['input', 'blur'] },
  ],
})

const sortChange = ({ prop, order }) => {
  searchInfo.value.sort_prop = prop
  searchInfo.value.sort_desc = order !== 'ascending'
  getTableData()
}

const onReset = () => {
  searchInfo.value = {}
  getTableData()
}

const onSubmit = () => {
  page.value = 1
  getTableData()
}

const pageSizeChange = (val) => {
  pageSize.value = val
  getTableData()
}

const pageChange = (val) => {
  page.value = val
  getTableData()
}

const execItem = (item) => {
  item.created_at_str = formatDate(item.created_at)
  return item
}

const getTableData = async () => {
  const table = await getSysMsgList({ page: page.value, page_size: pageSize.value, ...searchInfo.value })
  if (table.code === 0) {
    let resDataList = table.data.list || []
    let newList = []
    resDataList.forEach(item => {
      item = execItem(item)
      newList.push(item)
    })
    tableData.value = newList
    total.value = table.data.total
  }
}

const deleteRow = (row) => {
  ElMessageBox.confirm('确定删除?').then(() => {
    deleteInfoFunc(row)
  })
}

const initShowDialogInfo = async (row) => {
  const res = await findSysMsg({ id: row.id })
  if (res.code === 0) {
    formData.value = res.data || {}
    dialogShow.value = true
  }
}

const viewFunc = async (row) => {
  dType.value = 'view'
  dTitle.value = '查看'
  initShowDialogInfo(row)
}

const updateFunc = async (row) => {
  dType.value = 'update'
  dTitle.value = '更新'
  initShowDialogInfo(row)
}

const deleteInfoFunc = async (row) => {
  const res = await deleteSysMsg({ id: row.id })
  if (res.code === 0) {
    ElMessage({ type: 'success', message: '删除成功' })
    getTableData()
  }
}

const openDialog = () => {
  dType.value = 'create'
  dTitle.value = '创建'
  dialogShow.value = true
}

const closeDialog = () => {
  dialogShow.value = false
  formData.value = { title: '', content: '', }
}

const enterDialog = async () => {
  elFormRef.value?.validate(async (valid) => {
    if (!valid) return
    let res
    switch (dType.value) {
      case 'create':
        res = await createSysMsg(formData.value)
        break
      case 'update':
        res = await updateSysMsg(formData.value)
        break
      default:
        res = await createSysMsg(formData.value)
        break
    }
    if (res.code === 0) {
      ElMessage({ type: 'success', message: '创建/更改成功' })
      closeDialog()
      getTableData()
    }
  })
}

onBeforeMount(() => {
  getTableData()
})
</script>

<style></style>
