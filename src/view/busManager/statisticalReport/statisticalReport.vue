<template>
  <div>
    <el-card shadow="hover">
      <el-form :inline="true" :model="searchInfo" @keyup.enter="onSubmit">
        <el-form-item label="选择日期">
          <el-date-picker v-model="searchInfo.date_range" :shortcuts="dateShortcuts" type="datetimerange"
            value-format="YYYY-MM-DD HH:mm:ss" />
        </el-form-item>
        <el-form-item label="选车班车" prop="car">
          <el-select v-model="searchInfo.car" style="width: 400px !important" clearable multiple placeholder="请选择">
            <el-option v-for="item in carList" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="search" @click="onSubmit">查询</el-button>
          <el-button icon="refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>

      <div ref="echart" class="dashboard-line"></div>
    </el-card>
  </div>
</template>

<script setup>
// import { createGpsDevice, updateGpsDevice, deleteGpsDevice, deleteGpsDeviceByIds, findGpsDevice, getGpsDeviceList } from '@/api/retail/gpsDevice'
import { ref, reactive, nextTick, shallowRef, onMounted, onUnmounted, onBeforeMount } from 'vue'
import { dateShortcuts } from '@/utils/date_shortcuts'
import * as echarts from 'echarts'
import { getBusList } from '@/api/retail/bus'
import { dayjs } from 'element-plus'
const page = ref(1)
const searchInfo = ref({})
const onReset = () => {
  searchInfo.value = {}
  getTableData()
}

const onSubmit = () => {
  page.value = 1
  getTableData()
}

const carList = ref([])
const getTableData = async () => {
  const table = await getBusList({ page: 1, page_size: 1000 })
  if (table.code === 0) {
    let resDataList = table.data.list || []
    let datas = []
    carList.value = []
    resDataList.forEach((item) => {
      carList.value.push({
        id: item.id,
        name: `${item.bus_no}(${item.contacts})`,
      })
      datas.push(`${item.bus_no}(${item.contacts})`)
    })
    dataAxis.value = datas
    console.log(dataAxis.value)
  }
}

const dataAxis = ref([])

var data = [22, 18, 19, 23, 29, 30, 30, 13, 42, 31, 60, 49, 40, 40, 40, 40, 40, 40, 40, 60]
var data1 = [210, 142, 151, 284, 390, 430, 210, 123, 342, 221, 190, 349, 349, 349, 349, 349, 349, 349, 349, 349]
const colors = ['#5470C6', '#91CC75', '#EE6666']
const legend = ref(['工作时长(h)', '行驶里程(km)'])
const chart = shallowRef(null)
const echart = ref(null)
const resizeChart = () => {
  if (chart.value) {
    chart.value.resize()
  }
}

const initChart = () => {
  chart.value = echarts.init(echart.value)
  setOptions()
}
const setOptions = () => {
  chart.value.setOption({
    grid: {
      left: '40',
      right: '60',
      top: '100',
      bottom: '80',
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
      },
    },
    legend: {
      data: legend.value,
    },
    xAxis: [
      {
        data: dataAxis.value,
        axisTick: {
          show: false,
        },
        axisLine: {
          show: false,
        },
        z: 10,
      },
    ],
    yAxis: [
      {
        type: 'value',
        name: legend.value[0],
        position: 'left',

        alignTicks: true,
        axisLine: {
          show: true,
          lineStyle: {
            color: colors[0],
          },
        },
        axisLabel: {
          formatter: '{value} h',
        },
      },
      {
        type: 'value',
        name: legend.value[1],
        // offset: -80,

        position: 'right',
        alignTicks: true,
        axisLine: {
          show: true,
          lineStyle: {
            color: colors[1],
          },
        },
        axisLabel: {
          formatter: '{value} km',
        },
      },
    ],
    dataZoom: [
      {
        type: 'inside',
      },
      {
        type: 'slider',
      },
    ],
    toolbox: {
      feature: {
        magicType: { type: ['line', 'bar'] },
        restore: {},
        saveAsImage: {},
      },
    },
    series: [
      {
        name: legend.value[0],
        type: 'bar',

        itemStyle: {
          borderRadius: [5, 5, 0, 0],
          color: '#188df0',
        },
        emphasis: {
          itemStyle: {
            color: '#188df0',
          },
        },
        data: data,
      },
      {
        name: legend.value[1],
        type: 'bar',
        yAxisIndex: 1,
        itemStyle: {
          borderRadius: [5, 5, 0, 0],
          color: '#91CC75',
        },
        emphasis: {
          itemStyle: {
            color: '#91CC75',
          },
        },
        data: data1,
      },
    ],
  })
}

onUnmounted(() => {
  if (!chart.value) {
    return
  }
  chart.value.dispose()
  chart.value = null
  window.removeEventListener('resize', resizeChart)
})

onBeforeMount(async () => {
  //默认最近七天
  let start = dayjs().add(-7, 'day').startOf('day')
  let end = dayjs().endOf('day')
  searchInfo.value.date_range = [start, end]
  await getTableData()
  await nextTick()
  initChart()
  window.addEventListener('resize', resizeChart)
  // initChart()
})
</script>

<style>
.dashboard-line {
  background-color: #fff;
  height: 600px;
  width: 100%;
  margin-top: 60px;
}
</style>