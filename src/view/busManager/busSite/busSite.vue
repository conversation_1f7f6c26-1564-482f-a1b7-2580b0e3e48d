<template>
  <div>
    <el-card shadow="hover">
      <el-form :inline="true" :model="searchInfo" @keyup.enter="onSubmit">
        <el-form-item label="站点名称" prop="site_name">
          <el-input v-model="searchInfo.site_name" placeholder="请输入站点名称" />
        </el-form-item>
        <el-form-item label="站点类型" prop="site_type">
          <el-select v-model="searchInfo.site_type" clearable style="width: 100%" placeholder="请选择站点类型">
            <el-option v-for="item in siteTypeList" :key="item" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="经度" prop="longitude">
          <el-input v-model="searchInfo.longitude" placeholder="请输入经度" />
        </el-form-item>
        <el-form-item label="纬度" prop="latitude">
          <el-input v-model="searchInfo.latitude" placeholder="请输入纬度" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="search" @click="onSubmit">查询</el-button>
          <el-button icon="refresh" @click="onReset">重置</el-button>
          <el-button type="success" icon="plus" @click="openDialog">新增</el-button>
        </el-form-item>
      </el-form>
      <el-table ref="tableRef" :data="tableData" row-key="id" border @sort-change="sortChange" show-overflow-tooltip>
        <el-table-column align="center" label="ID" prop="id" :width="100" fixed="left" />
        <el-table-column align="center" label="创建时间" prop="created_at_str" :width="160" />
        <el-table-column align="center" label="更新时间" prop="created_up_str" :width="160" />

        <el-table-column align="center" label="站点名称" prop="site_name" :min-width="120" />
        <el-table-column align="center" label="站点类型" :width="120">
          <template #default="scope">
            {{siteTypeMap[scope.row.site_type]}}
          </template>
        </el-table-column>
        <el-table-column align="center" label="经度" prop="longitude" :min-width="120" />
        <el-table-column align="center" label="纬度" prop="latitude" :min-width="120" />
        <el-table-column align="center" label="操作" :width="280" fixed="right">
          <template #default="scope">
            <el-button type="primary" @click="viewFunc(scope.row)">查看详情</el-button>
            <el-button type="warning" @click="updateFunc(scope.row)">变更</el-button>
            <el-button type="danger" @click="deleteRow(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :layout="PageLayout"
        :current-page="page"
        :page-size="pageSize"
        :page-sizes="PageSizes"
        :total="total"
        @current-change="pageChange"
        @size-change="pageSizeChange"
      />
    </el-card>
    <el-dialog v-model="dialogShow" :before-close="closeDialog" :title="dTitle" top="10px" destroy-on-close>
      <el-form :model="formData" :disabled="dType === 'view'" label-position="right" ref="elFormRef" :rules="formDataRule" label-width="80px">
        <el-form-item label="站点名称" prop="site_name">
          <el-input v-model="formData.site_name" :maxlength="64" placeholder="请输入站点名称" show-word-limit clearable />
        </el-form-item>
        <el-form-item label="站点类型" prop="site_type">
          <el-select v-model="formData.site_type" clearable style="width: 100%" placeholder="请选择站点类型">
            <el-option v-for="item in siteTypeList" :key="item" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="经度" prop="longitude">
          <el-input-number v-model="formData.longitude" placeholder="请输入经度" style="width: 100%" :maxlength="64" show-word-limit clearable />
        </el-form-item>
        <el-form-item label="纬度" prop="latitude">
          <el-input-number v-model="formData.latitude" placeholder="请输入纬度" style="width: 100%" :maxlength="64" show-word-limit clearable />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer" v-show="dType !== 'view'">
          <el-button @click="closeDialog">取 消</el-button>
          <el-button type="primary" @click="enterDialog">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { createBusSite, updateBusSite, deleteBusSite, deleteBusSiteByIds, findBusSite, getBusSiteList } from '@/api/retail/busSite'
import { formatDate } from '@/utils/format'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ref, reactive, onBeforeMount } from 'vue'
import { PageLayout, PageSizes } from '@/utils/const'
import { useRouter, useRoute } from 'vue-router'
import { useUserStore } from '@/pinia/modules/user'

const userStore = useUserStore()
const router = useRouter()
const route = useRoute()
const dialogShow = ref(false)
const dTitle = ref('')
const dType = ref('')
const elFormRef = ref()
const page = ref(1)
const total = ref(0)
const pageSize = ref(100)
const tableData = ref([])
const searchInfo = ref({})
const formData = ref({ site_name: '' })
const formDataRule = reactive({
  site_name: [
    { required: true, message: '站点名称不能为空', trigger: ['change'] },
    { min: 1, max: 64, message: '站点名称长度在 1 到 64 个字符', trigger: ['change'] }
  ],
  site_type: [
    { required: true, message: '站点类型不能为空', trigger: ['change'] },
  ],
  longitude: [
    { required: true, message: '经度不能为空', trigger: ['change'] }
  ],
  latitude: [
    { required: true, message: '纬度不能为空', trigger: ['change'] }
  ]
})
const siteTypeList = [
  {
    value: 1,
    label: '活力启程线'
  },
  {
    value: 2,
    label: '暖心归巢线'
  }
]

const siteTypeMap = {
  1: '活力启程线',
  2: '暖心归巢线'
}

const sortChange = ({ prop, order }) => {
  searchInfo.value.sort_prop = prop
  searchInfo.value.sort_desc = order !== 'ascending'
  getTableData()
}

const onReset = () => {
  searchInfo.value = {}
  getTableData()
}

const onSubmit = () => {
  page.value = 1
  getTableData()
}

const pageSizeChange = (val) => {
  pageSize.value = val
  getTableData()
}

const pageChange = (val) => {
  page.value = val
  getTableData()
}

const execItem = (item) => {
  item.created_at_str = formatDate(item.created_at)
  item.created_up_str = formatDate(item.updated_at)
  return item
}

const getTableData = async () => {
  const table = await getBusSiteList({ page: page.value, page_size: pageSize.value, ...searchInfo.value })
  if (table.code === 0) {
    let resDataList = table.data.list || []
    let newList = []
    resDataList.forEach((item) => {
      item = execItem(item)
      newList.push(item)
    })
    tableData.value = newList
    total.value = table.data.total
  }
}

const deleteRow = (row) => {
  ElMessageBox.confirm('确定删除?').then(() => {
    deleteInfoFunc(row)
  })
}

const initShowDialogInfo = async (row) => {
  const res = await findBusSite({ id: row.id })
  if (res.code === 0) {
    formData.value = res.data || {}
    dialogShow.value = true
  }
}

const viewFunc = async (row) => {
  dType.value = 'view'
  dTitle.value = '查看'
  initShowDialogInfo(row)
}

const updateFunc = async (row) => {
  dType.value = 'update'
  dTitle.value = '更新'
  initShowDialogInfo(row)
}

const deleteInfoFunc = async (row) => {
  const res = await deleteBusSite({ id: row.id })
  if (res.code === 0) {
    ElMessage({ type: 'success', message: '删除成功' })
    getTableData()
  }
}

const openDialog = () => {
  dType.value = 'create'
  dTitle.value = '创建'
  dialogShow.value = true
}

const closeDialog = () => {
  dialogShow.value = false
  formData.value = { site_name: '' }
}

const enterDialog = async () => {
  elFormRef.value?.validate(async (valid) => {
    if (!valid) return
    let res
    switch (dType.value) {
      case 'create':
        res = await createBusSite(formData.value)
        break
      case 'update':
        res = await updateBusSite(formData.value)
        break
      default:
        res = await createBusSite(formData.value)
        break
    }
    if (res.code === 0) {
      ElMessage({ type: 'success', message: '创建/更改成功' })
      closeDialog()
      getTableData()
    }
  })
}

onBeforeMount(() => {
  getTableData()
})
</script>

<style></style>
