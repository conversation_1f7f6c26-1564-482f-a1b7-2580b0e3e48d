<template>
  <div>
    <el-card shadow="hover">
      <el-form :inline="true" :model="searchInfo" @keyup.enter="onSubmit">
            <el-form-item label="bus_route_id字段" prop="bus_route_id">
                
                  <el-input v-model.number="searchInfo.bus_route_id" placeholder="请输入" />
              </el-form-item>
            <el-form-item label="bus_site_id字段" prop="bus_site_id">
                
                  <el-input v-model.number="searchInfo.bus_site_id" placeholder="请输入" />
              </el-form-item>
            <el-form-item label="is_start_site字段" prop="is_start_site">
                
                  <el-input v-model.number="searchInfo.is_start_site" placeholder="请输入" />
              </el-form-item>
            <el-form-item label="is_en_site字段" prop="is_en_site">
                
                  <el-input v-model.number="searchInfo.is_en_site" placeholder="请输入" />
              </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="search" @click="onSubmit">查询</el-button>
            <el-button icon="refresh" @click="onReset">重置</el-button>
            <el-button type="success" icon="plus" @click="openDialog">新增</el-button>
          </el-form-item>
        </el-form>
        <el-table ref="tableRef" :data="tableData" row-key="id" border @sort-change="sortChange" show-overflow-tooltip>
                <el-table-column align="center" label="ID" prop="id" :width="100" fixed="left" />
                <el-table-column align="center" label="创建时间" prop="created_at_str" :width="160"/>
                <el-table-column align="center" label="bus_route_id字段" prop="bus_route_id" :min-width="120" />
                <el-table-column align="center" label="bus_site_id字段" prop="bus_site_id" :min-width="120" />
                <el-table-column align="center" label="is_start_site字段" prop="is_start_site" :min-width="120" />
                <el-table-column align="center" label="is_en_site字段" prop="is_en_site" :min-width="120" />
                <el-table-column align="center" label="route_sort字段" prop="route_sort" :min-width="120" />
                <el-table-column align="center" label="time_column字段" prop="time_column" :min-width="120" />
                <el-table-column align="center" label="操作" :width="280" fixed="right">
                    <template #default="scope">
                    <el-button type="primary" @click="viewFunc(scope.row)">查看详情</el-button>
                    <el-button type="warning" @click="updateFunc(scope.row)">变更</el-button>
                    <el-button type="danger" @click="deleteRow(scope.row)">删除</el-button>
                    </template>
                </el-table-column>
                </el-table>
                <el-pagination
                  :layout="PageLayout"
                  :current-page="page"
                  :page-size="pageSize"
                  :page-sizes="PageSizes"
                  :total="total"
                  @current-change="pageChange"
                  @size-change="pageSizeChange"
                />
  </el-card>
    <el-dialog v-model="dialogShow" :before-close="closeDialog" :title="dTitle" top="10px" destroy-on-close>
      <el-form :model="formData" :disabled="dType === 'view'" label-position="right" ref="elFormRef" :rules="formDataRule" label-width="80px">
        <el-form-item label="bus_route_id字段" prop="bus_route_id" >
        <el-input-number v-model="formData.bus_route_id" style="width:100%"/>
        </el-form-item>
        <el-form-item label="bus_site_id字段" prop="bus_site_id" >
        <el-input-number v-model="formData.bus_site_id" style="width:100%"/>
        </el-form-item>
        <el-form-item label="is_start_site字段" prop="is_start_site" >
        <el-input-number v-model="formData.is_start_site" style="width:100%"/>
        </el-form-item>
        <el-form-item label="is_en_site字段" prop="is_en_site" >
        <el-input-number v-model="formData.is_en_site" style="width:100%"/>
        </el-form-item>
        <el-form-item label="route_sort字段" prop="route_sort" >
        <el-input-number v-model="formData.route_sort" style="width:100%"/>
        </el-form-item>
        <el-form-item label="time_column字段" prop="time_column" >
          <el-date-picker v-model="formData.time_column" type="date" style="width:100%" placeholder="选择日期" clearable  />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer" v-show="dType !== 'view'">
          <el-button @click="closeDialog">取 消</el-button>
          <el-button type="primary" @click="enterDialog">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import {
  createBusRouteSiteRelation,
  updateBusRouteSiteRelation,
  deleteBusRouteSiteRelation,
  deleteBusRouteSiteRelationByIds,
  findBusRouteSiteRelation,
  getBusRouteSiteRelationList
} from '@/api/retail/busRouteSiteRelation'
import { formatDate } from '@/utils/format'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ref, reactive, onBeforeMount } from 'vue'
import { PageLayout, PageSizes } from "@/utils/const"
import { useRouter, useRoute } from 'vue-router'
import {useUserStore} from "@/pinia/modules/user";


const userStore = useUserStore()
const router = useRouter()
const route = useRoute()
const dialogShow = ref(false)
const dTitle = ref('')
const dType = ref('')
const elFormRef = ref()
const page = ref(1)
const total = ref(0)
const pageSize = ref(100)
const tableData = ref([])
const searchInfo = ref({})
const formData = ref({bus_route_id: 0,bus_site_id: 0,is_start_site: 0,is_en_site: 0,route_sort: 0,time_column: new Date(),})
const formDataRule = reactive({})

const sortChange = ({ prop, order }) => {
  searchInfo.value.sort_prop = prop
  searchInfo.value.sort_desc = order !== 'ascending'
  getTableData()
}

const onReset = () => {
  searchInfo.value = {}
  getTableData()
}

const onSubmit = () => {
  page.value = 1
  getTableData()
}

const pageSizeChange = (val) => {
  pageSize.value = val
  getTableData()
}

const pageChange = (val) => {
  page.value = val
  getTableData()
}

const execItem = (item) => {
  item.created_at_str = formatDate(item.created_at)
  return item
}

const getTableData = async() => {
  const table = await getBusRouteSiteRelationList({ page: page.value, page_size: pageSize.value, ...searchInfo.value })
  if (table.code === 0) {
    let resDataList = table.data.list || []
    let newList = []
    resDataList.forEach(item => {
      item = execItem(item)
      newList.push(item)
    })
    tableData.value = newList
    total.value = table.data.total
  }
}

const deleteRow = (row) => {
  ElMessageBox.confirm('确定删除?').then(() => {
    deleteInfoFunc(row)
  })
}

const initShowDialogInfo = async (row) => {
  const res = await findBusRouteSiteRelation({id: row.id})
  if (res.code === 0) {
    formData.value = res.data || {}
    dialogShow.value = true
  }
}

const viewFunc = async (row) => {
  dType.value = 'view'
  dTitle.value = '查看'
  initShowDialogInfo(row)
}

const updateFunc = async (row) => {
  dType.value = 'update'
  dTitle.value = '更新'
  initShowDialogInfo(row)
}

const deleteInfoFunc = async (row) => {
  const res = await deleteBusRouteSiteRelation({ id: row.id })
  if (res.code === 0) {
    ElMessage({type: 'success',message: '删除成功'})
    getTableData()
  }
}

const openDialog = () => {
  dType.value = 'create'
  dTitle.value = '创建'
  dialogShow.value = true
}

const closeDialog = () => {
  dialogShow.value = false
  formData.value = {bus_route_id: 0,bus_site_id: 0,is_start_site: 0,is_en_site: 0,route_sort: 0,time_column: new Date(),}
}

const enterDialog = async () => {
  elFormRef.value?.validate( async (valid) => {
    if (!valid) return
    let res
    switch (dType.value) {
      case 'create':
        res = await createBusRouteSiteRelation(formData.value)
        break
      case 'update':
        res = await updateBusRouteSiteRelation(formData.value)
        break
      default:
        res = await createBusRouteSiteRelation(formData.value)
        break
    }
    if (res.code === 0) {
      ElMessage({type: 'success', message: '创建/更改成功'})
      closeDialog()
      getTableData()
    }
  })
}

onBeforeMount(()=>{
  getTableData()
})
</script>

<style></style>
