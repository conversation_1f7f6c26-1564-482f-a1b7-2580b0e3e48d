// 对Date的扩展，将 Date 转化为指定格式的String
// 月(M)、日(d)、小时(h)、分(m)、秒(s)、季度(q) 可以用 1-2 个占位符，
// 年(y)可以用 1-4 个占位符，毫秒(S)只能用 1 个占位符(是 1-3 位的数字)
// (new Date()).Format("yyyy-MM-dd hh:mm:ss.S") ==> 2006-07-02 08:09:04.423
// (new Date()).Format("yyyy-M-d h:m:s.S")      ==> 2006-7-2 8:9:4.18
// eslint-disable-next-line no-extend-native
import {  dayjs } from 'element-plus'

Date.prototype.Format = function(fmt) {
  var o = {
    'M+': this.getMonth() + 1, // 月份
    'd+': this.getDate(), // 日
    'h+': this.getHours(), // 小时
    'm+': this.getMinutes(), // 分
    's+': this.getSeconds(), // 秒
    'q+': Math.floor((this.getMonth() + 3) / 3), // 季度
    'S': this.getMilliseconds() // 毫秒
  }
  if (/(y+)/.test(fmt)) { fmt = fmt.replace(RegExp.$1, (this.getFullYear() + '').substr(4 - RegExp.$1.length)) }
  for (var k in o) {
    if (new RegExp('(' + k + ')').test(fmt)) { fmt = fmt.replace(RegExp.$1, (RegExp.$1.length === 1) ? (o[k]) : (('00' + o[k]).substr(('' + o[k]).length))) }
  }
  return fmt
}

export function formatTimeToStr(times, pattern) {
  var d = new Date(times).Format('yyyy-MM-dd hh:mm:ss')
  if (pattern) {
    d = new Date(times).Format(pattern)
  }
  return d.toLocaleString()
}

export function generateLastWeekDates(){
  const dates = [];
  for (let i = 0; i < 7; i++) {
    dates.push(dayjs().add(i, 'day').format('YYYY-MM-DD'));
  }
  return dates;
};


export function generateNextMonthDates() {
  const dates = [];
  const today = dayjs();
  const endDate = today.add(1, 'month');
  const daysInMonth = endDate.diff(today, 'day');
  
  for (let i = 0; i <= daysInMonth; i++) {
    dates.push(today.add(i, 'day').format('YYYY-MM-DD'));
  }
  return dates;
};

export function generateNextHalfYearDates (){
  const dates = [];
  const today = dayjs();
  const endDate = today.add(6, 'month');
  const daysInHalfYear = endDate.diff(today, 'day');
  
  for (let i = 0; i <= daysInHalfYear; i++) {
    dates.push(today.add(i, 'day').format('YYYY-MM-DD'));
  }
  return dates;
};

export function generateNextYearDates(){
  const dates = [];
  const today = dayjs();
  const endDate = today.add(1, 'year');
  const daysInYear = endDate.diff(today, 'day');
  
  for (let i = 0; i <= daysInYear; i++) {
    dates.push(today.add(i, 'day').format('YYYY-MM-DD'));
  }
  return dates;
};
