import {dayjs} from "element-plus";

export const dateShortcuts = [
  {
    text: '昨日',
    value: () => {
      let start = dayjs().add(-1, 'day').startOf('day')
      let end = dayjs().add(-1, 'day').endOf('day')
      return [start, end]
    },
  },
  {
    text: '今日',
    value: () => {
      let start = dayjs().startOf('day')
      let end = dayjs().endOf('day')
      return [start, end]
    },
  },
  {
    text: '上周',
    value: () => {
      let start = dayjs().add(-1, 'week').startOf('week')
      let end = dayjs().add(-1, 'week').endOf('week')
      return [start, end]
    },
  },
  {
    text: '本周',
    value: () => {
      let start = dayjs().startOf('week')
      let end = dayjs().endOf('week')
      return [start, end]
    },
  },
  {
    text: '上月',
    value: () => {
      let start = dayjs().add(-1, 'month').startOf('month')
      let end = dayjs().add(-1, 'month').endOf('month')
      return [start, end]
    },
  },
  {
    text: '本月',
    value: () => {
      let start = dayjs().startOf('month')
      let end = dayjs().endOf('month')
      return [start, end]
    },
  },
  {
    text: '近7天',
    value: () => {
      let start = dayjs().add(-7, 'day').startOf('day')
      let end = dayjs().endOf('day')
      return [start, end]
    },
  },
  {
    text: '近14天',
    value: () => {
      let start = dayjs().add(-14, 'day').startOf('day')
      let end = dayjs().endOf('day')
      return [start, end]
    },
  },
  {
    text: '近30天',
    value: () => {
      let start = dayjs().add(-30, 'day').startOf('day')
      let end = dayjs().endOf('day')
      return [start, end]
    },
  },
]
