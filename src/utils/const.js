export const VITE_OSS_PREFIX = import.meta.env.VITE_OSS_PREFIX
export const PageSizes = [10, 30, 50, 100]
export const PageSizesLarge = [10, 100, 500, 1000, 2000, 5000, 10000]
export const PageLayout = "total, sizes, prev, pager, next, jumper"
export const Units = ['份', '个','罐', '盒', '条', '包', '瓶', '斤', '件', '套', '张', '双','桶','碗','箱'];

export const CatTypeTakeaway = 1
export const CatTypeGroupBuy = 2

export const FileTypeAvatar = 1
export const FileTypeFeedback = 2
export const FileTypeTakeaway = 3
export const FileTypeGroupBuy = 4
export const FileTypeNotifyMsgPreviewImg = 5
export const FileTypeNotifyMsgContent = 6
export const TestPicB64 = 'data:image/png;base64,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'
