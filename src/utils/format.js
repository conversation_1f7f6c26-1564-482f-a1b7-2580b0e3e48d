import {formatTimeToStr} from '@/utils/date'
import {getDict} from '@/utils/dictionary'
import {dayjs} from "element-plus";
import {VITE_OSS_PREFIX} from "@/utils/const";


export const getOssFileUrl = (img_url) => {
    // 判断img_url是否是以http开头的
    if (!img_url) {
        return ''
    }
    if (img_url.slice(0, 4) === 'http') {
        return img_url
    } else {
        return `${VITE_OSS_PREFIX}${img_url}`
    }
}

export const getOssFileUrlList = (img_url) => {
    const urlList = []
    if (!img_url) {
        return urlList
    }
    if (img_url.length === 0) {
        return urlList
    }

    for (const url of img_url) {
        if (url.slice(0, 4) === 'http') {
            urlList.push(url)
        } else {
            urlList.push(`${VITE_OSS_PREFIX}${url}`)
        }
    }
    return urlList
}

export const formatBoolean = (bool) => {
    if (bool !== null) {
        return bool ? '是' : '否'
    } else {
        return ''
    }
}
export const formatDate = (time) => {
    if (time !== null && time !== '') {
        var date = new Date(time)
        return formatTimeToStr(date, 'yyyy-MM-dd hh:mm:ss')
    } else {
        return ''
    }
}
export const formatDateToBj = (time) => {
    if (time !== null && time !== '') {
        dayjs(time).tz('Asia/Shanghai').format('YYYY-MM-DD HH:mm:ss')
    } else {
        return ''
    }
}
export const filterDict = (value, options) => {
    const rowLabel = options && options.filter(item => item.value === value)
    return rowLabel && rowLabel[0] && rowLabel[0].label
}

export const getDictFunc = async (type) => {
    const dicts = await getDict(type)
    return dicts
}

const path = import.meta.env.VITE_BASE_PATH + ':' + import.meta.env.VITE_SERVER_PORT + '/'
export const ReturnArrImg = (arr) => {
    const imgArr = []
    if (arr instanceof Array) { // 如果是数组类型
        for (const arrKey in arr) {
            if (arr[arrKey].slice(0, 4) !== 'http') {
                imgArr.push(path + arr[arrKey])
            } else {
                imgArr.push(arr[arrKey])
            }
        }
    } else { // 如果不是数组类型
        if (arr.slice(0, 4) !== 'http') {
            imgArr.push(path + arr)
        } else {
            imgArr.push(arr)
        }
    }
    return imgArr
}

export const onDownloadFile = (url) => {
    window.open(path + url)
}
